<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>家国情怀：从历史传承到时代担当 - 交互式PPT</title>
    <script src="https://unpkg.byted-static.com/coze/space_ppt_lib/1.0.3-alpha.12/lib/tailwindcss.js"></script>
    <script src="https://unpkg.byted-static.com/chart.js/4.5.0/dist/chart.umd.js"></script>
    <script src="https://unpkg.byted-static.com/fortawesome/fontawesome-free/6.7.2/js/all.min.js" data-auto-replace-svg="nest"></script>
    <link href="https://lf-code-agent.coze.cn/obj/x-ai-cn/fonts/google/google-all-fonts.css" rel="stylesheet"/>
    <style>
        .font-noto { font-family: 'Noto Sans SC', sans-serif; }
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Noto Sans SC', sans-serif;
        }

        /* 新拟物化基础样式 */
        .neumorphism {
            background: #f0f0f3;
            border-radius: 20px;
            box-shadow:
                20px 20px 60px #bebebe,
                -20px -20px 60px #ffffff;
        }

        .neumorphism-inset {
            background: #f0f0f3;
            border-radius: 20px;
            box-shadow:
                inset 20px 20px 60px #bebebe,
                inset -20px -20px 60px #ffffff;
        }

        .neumorphism-dark {
            background: #2d3748;
            border-radius: 20px;
            box-shadow:
                20px 20px 60px #1a202c,
                -20px -20px 60px #404a5c;
        }

        /* 几何装饰元素 */
        .geometric-bg {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .geometric-shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .geometric-shape:nth-child(1) {
            top: 10%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 50%;
            animation-delay: 0s;
        }

        .geometric-shape:nth-child(2) {
            top: 20%;
            right: 15%;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #48dbfb, #0abde3);
            transform: rotate(45deg);
            animation-delay: 2s;
        }

        .geometric-shape:nth-child(3) {
            bottom: 20%;
            left: 20%;
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #ff9ff3, #f368e0);
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* PPT容器样式 */
        .ppt-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* 单页幻灯片样式 - 优化动画性能 */
        .slide {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transition: opacity 0.8s ease, transform 0.8s ease; /* 分离属性过渡，提高性能 */
            display: flex;
            align-items: center;
            justify-content: center;
            transform: scale(0.98) translateY(20px); /* 减小变换幅度，提高性能 */
            will-change: opacity, transform; /* 提示浏览器优化这些属性的变化 */
        }
        .slide.active {
            opacity: 1;
            z-index: 10;
            transform: scale(1) translateY(0);
        }
        .slide.next {
            transform: translateX(100%) scale(0.95);
            z-index: 5; /* 添加z-index管理层级 */
        }
        .slide.prev {
            transform: translateX(-100%) scale(0.95);
            z-index: 5; /* 添加z-index管理层级 */
        }

        /* AI智能控制栏样式 */
        .control-bar {
            position: fixed;
            bottom: 30px; /* 增加底部距离，避免遮挡 */
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000; /* 提高z-index确保在最上层 */
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 15px 25px;
            background: rgba(255, 255, 255, 0.25); /* 增加透明度，提高可见性 */
            border-radius: 30px;
            max-width: 80%; /* 使用百分比宽度，提高响应式适配 */
            width: auto; /* 自适应宽度 */
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3); /* 增强边框可见性 */
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: visible; /* 确保内容不被裁剪 */
        }
        .control-bar:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateX(-50%) translateY(-3px);
            box-shadow:
                0 15px 45px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
        
        /* 添加响应式布局，确保在小屏幕上控制栏正常显示 */
        @media (max-width: 768px) {
            .control-bar {
                padding: 10px 15px;
                gap: 10px;
                max-width: 90%;
                bottom: 20px;
            }
            
            .control-btn {
                width: 40px;
                height: 40px;
            }
            
            .slide-info {
                font-size: 12px;
            }
        }
        
        @media (max-width: 480px) {
            .control-bar {
                padding: 8px 12px;
                gap: 8px;
                max-width: 95%;
                bottom: 15px;
            }
            
            .control-btn {
                width: 36px;
                height: 36px;
            }
        }

        /* 新拟物化按钮 */
        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(145deg, #f0f0f3, #cacaca);
            color: #667eea;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                6px 6px 12px #bebebe,
                -6px -6px 12px #ffffff;
            position: relative;
            overflow: hidden;
            flex-shrink: 0; /* 防止按钮被压缩 */
            font-size: 1rem; /* 使用相对单位 */
        }

        .control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(145deg, transparent, rgba(255, 255, 255, 0.1));
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow:
                12px 12px 24px #bebebe,
                -12px -12px 24px #ffffff;
            color: #5a67d8;
        }

        .control-btn:hover::before {
            opacity: 1;
        }

        .control-btn:active {
            transform: translateY(0);
            box-shadow:
                inset 8px 8px 16px #bebebe,
                inset -8px -8px 16px #ffffff;
        }

        .control-btn.audio-btn {
            background: linear-gradient(145deg, #ff6b6b, #ee5a52);
            color: white;
            box-shadow:
                8px 8px 16px #d63031,
                -8px -8px 16px #ff7675;
        }

        .control-btn.audio-btn:hover {
            background: linear-gradient(145deg, #ee5a52, #d63031);
            box-shadow:
                12px 12px 24px #d63031,
                -12px -12px 24px #ff7675;
        }

        /* 进度条新拟物化设计 */
        .progress-container {
            flex: 1;
            min-width: 100px; /* 确保最小宽度 */
            height: 8px;
            background: #f0f0f3;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            box-shadow:
                inset 3px 3px 6px #bebebe,
                inset -3px -3px 6px #ffffff;
            position: relative;
            margin: 0 10px; /* 添加左右边距 */
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
            position: relative;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
            border-radius: 0 10px 10px 0;
        }

        .slide-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-family: 'Noto Sans SC', sans-serif;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* AI智能逐点显示元素样式 - 优化性能 */
        .reveal-item {
            opacity: 0;
            transform: translateY(30px) scale(0.95); /* 减小变换幅度 */
            transition: opacity 0.6s ease, transform 0.6s ease; /* 分离属性过渡，缩短动画时间 */
            position: relative;
            will-change: opacity, transform; /* 提示浏览器优化 */
        }

        .reveal-item.visible {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .reveal-item::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
            border-radius: 10px;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
            filter: blur(10px);
        }

        .reveal-item:hover::before {
            opacity: 0.3;
        }

        /* AI音频播放按钮新拟物化设计 */
        .audio-play-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(145deg, #ff6b6b, #ee5a52);
            color: white;
            cursor: pointer;
            margin-left: 10px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            vertical-align: middle;
            box-shadow:
                6px 6px 12px rgba(238, 90, 82, 0.3),
                -6px -6px 12px rgba(255, 107, 107, 0.3);
            position: relative;
            overflow: hidden;
        }

        .audio-play-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(145deg, transparent, rgba(255, 255, 255, 0.2));
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .audio-play-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow:
                8px 8px 16px rgba(238, 90, 82, 0.4),
                -8px -8px 16px rgba(255, 107, 107, 0.4);
        }

        .audio-play-btn:hover::before {
            opacity: 1;
        }

        .audio-play-btn.playing {
            animation: aiPulse 2s infinite;
        }

        @keyframes aiPulse {
            0% {
                transform: scale(1);
                box-shadow:
                    6px 6px 12px rgba(238, 90, 82, 0.3),
                    -6px -6px 12px rgba(255, 107, 107, 0.3),
                    0 0 0 0 rgba(255, 107, 107, 0.7);
            }
            50% {
                transform: scale(1.1);
                box-shadow:
                    8px 8px 16px rgba(238, 90, 82, 0.4),
                    -8px -8px 16px rgba(255, 107, 107, 0.4),
                    0 0 0 15px rgba(255, 107, 107, 0);
            }
            100% {
                transform: scale(1);
                box-shadow:
                    6px 6px 12px rgba(238, 90, 82, 0.3),
                    -6px -6px 12px rgba(255, 107, 107, 0.3),
                    0 0 0 0 rgba(255, 107, 107, 0);
            }
        }

        /* AI智能动画样式 - 优化性能 */
        @keyframes aiGlow {
            0%, 100% {
                box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 25px rgba(102, 126, 234, 0.5);
                transform: scale(1.01); /* 减小缩放幅度 */
            }
        }

        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(20px); } /* 减小移动距离 */
            100% { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            0% { transform: translateY(40px) scale(0.98); opacity: 0; } /* 简化动画，移除模糊效果 */
            100% { transform: translateY(0) scale(1); opacity: 1; }
        }

        @keyframes scaleIn {
            0% { transform: scale(0.8) rotate(-5deg); opacity: 0; filter: blur(5px); }
            100% { transform: scale(1) rotate(0deg); opacity: 1; filter: blur(0); }
        }

        @keyframes pulseHighlight {
            0% {
                background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.2));
                transform: scale(1);
            }
            50% {
                background: linear-gradient(45deg, rgba(255, 215, 0, 0.4), rgba(255, 193, 7, 0.4));
                transform: scale(1.05);
            }
            100% {
                background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.2));
                transform: scale(1);
            }
        }

        @keyframes underlinePulse {
            0% {
                text-decoration-thickness: 2px;
                opacity: 1;
                text-shadow: 0 0 10px rgba(255, 71, 87, 0.3);
            }
            50% {
                text-decoration-thickness: 4px;
                opacity: 0.8;
                text-shadow: 0 0 20px rgba(255, 71, 87, 0.6);
            }
            100% {
                text-decoration-thickness: 2px;
                opacity: 1;
                text-shadow: 0 0 10px rgba(255, 71, 87, 0.3);
            }
        }

        @keyframes morphingGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .animate-fade-in { animation: fadeIn 1s cubic-bezier(0.4, 0, 0.2, 1) forwards; }
        .animate-slide-up { animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1) forwards; }
        .animate-scale-in { animation: scaleIn 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards; }
        .animate-pulse-highlight {
            animation: pulseHighlight 3s infinite;
            padding: 2px 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .animate-underline-pulse {
            animation: underlinePulse 2s infinite;
            text-decoration: underline;
            text-decoration-color: #ff4757;
            position: relative;
        }

        .ai-glow { animation: aiGlow 4s ease-in-out infinite; }

        .text-shadow {
            text-shadow:
                0 2px 10px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(102, 126, 234, 0.2);
        }

        .bg-radial { background-image: radial-gradient(circle, var(--tw-gradient-stops)); }

        .morphing-gradient {
            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 400% 400%;
            animation: morphingGradient 8s ease infinite;
        }

        /* AI智能名言新拟物化样式 */
        .quote-container {
            position: relative;
            padding: 20px;
            background: rgba(240, 240, 243, 0.95);
            border-radius: 20px;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            overflow: hidden;
            margin-bottom: 12px;
            box-shadow:
                15px 15px 30px rgba(190, 190, 190, 0.3),
                -15px -15px 30px rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .quote-container:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow:
                20px 20px 40px rgba(190, 190, 190, 0.4),
                -20px -20px 40px rgba(255, 255, 255, 0.4),
                0 10px 30px rgba(102, 126, 234, 0.2);
        }

        .quote-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(102, 126, 234, 0.1),
                rgba(118, 75, 162, 0.1),
                rgba(240, 147, 251, 0.1),
                rgba(245, 87, 108, 0.1));
            opacity: 0;
            transition: opacity 0.5s ease;
            z-index: 1;
            border-radius: 20px;
        }

        .quote-container:hover::before {
            opacity: 1;
        }

        .quote-container::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
            border-radius: 22px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .quote-container:hover::after {
            opacity: 0.3;
        }

        .quote-text {
            font-style: italic;
            position: relative;
            z-index: 2;
            line-height: 1.7;
            color: #2d3748;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        }

        .quote-mark {
            position: absolute;
            font-size: 80px;
            z-index: 1;
            opacity: 0.08;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: bold;
        }

        .quote-mark.left {
            top: -15px;
            left: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .quote-mark.right {
            bottom: -40px;
            right: 15px;
            background: linear-gradient(45deg, #f093fb, #f5576c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .quote-container:hover .quote-mark {
            opacity: 0.15;
            transform: scale(1.1) rotate(5deg);
        }

        /* 音频播放动画 */
        .audio-wave {
            display: inline-flex;
            align-items: center;
            gap: 2px;
            margin-left: 8px;
        }
        .wave-bar {
            width: 3px;
            background: #ff4757;
            border-radius: 2px;
            animation: wave 1.2s ease-in-out infinite;
        }
        .wave-bar:nth-child(1) { animation-delay: 0s; }
        .wave-bar:nth-child(2) { animation-delay: 0.1s; }
        .wave-bar:nth-child(3) { animation-delay: 0.2s; }
        .wave-bar:nth-child(4) { animation-delay: 0.3s; }
        .wave-bar:nth-child(5) { animation-delay: 0.4s; }

        @keyframes wave {
            0%, 40%, 100% { transform: scaleY(0.4); }
            20% { transform: scaleY(1); }
        }

        /* 第10页特殊样式 - 避免控制栏遮挡 */
        #slide10 .relative.z-30 {
            padding-bottom: 120px !important;
        }

        /* 响应式调整 */
        @media (max-height: 800px) {
            #slide10 .quote-container {
                padding: 12px;
                margin-bottom: 4px;
            }
            #slide10 .text-base {
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body class="font-noto">
    <!-- 音频元素（隐藏） -->
    <audio id="audioPlayer" preload="none">
        <source src="" type="audio/mpeg">
    </audio>
    
    <!-- PPT容器 -->
    <div class="ppt-container" id="pptContainer">
        <!-- 第1页：AI智能封面 -->
        <div class="slide active" id="slide1" data-reveal-count="4">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden morphing-gradient">
                <!-- 几何装饰背景 -->
                <div class="geometric-bg">
                    <div class="geometric-shape"></div>
                    <div class="geometric-shape"></div>
                    <div class="geometric-shape"></div>
                </div>

                <!-- 主背景图片 -->
                <img alt="蜿蜒于山峦间的长城剪影，展现历史厚重感" class="absolute inset-0 w-full h-full z-0 opacity-30 object-cover object-top animate-scale-in" src="https://s.coze.cn/image/mRjvTG5ZzMM/"/>

                <!-- AI光环效果 -->
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-radial from-[#667eea]/20 via-[#764ba2]/10 to-transparent rounded-full pointer-events-none z-10 ai-glow"></div>
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-radial from-[#f093fb]/15 to-transparent rounded-full pointer-events-none z-10 ai-glow" style="animation-delay: 2s;"></div>

                <!-- 新拟物化内容容器 -->
                <div class="relative z-20 flex flex-col items-center justify-center text-center p-12">
                    <!-- AI智能标识 -->
                    <div class="neumorphism-dark px-6 py-3 mb-8 reveal-item">
                        <div class="flex items-center gap-3 text-white">
                            <i class="fas fa-brain text-[#667eea] text-xl"></i>
                            <span class="text-sm font-medium tracking-wider">AI智能教育系统</span>
                            <div class="w-2 h-2 bg-[#2ed573] rounded-full animate-pulse"></div>
                        </div>
                    </div>

                    <!-- 主标题 -->
                    <div class="neumorphism p-8 mb-8 reveal-item" data-delay="100">
                        <h1 class="text-5xl font-black tracking-wider text-shadow" style="background: linear-gradient(135deg, #667eea, #764ba2, #f093fb); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                            家国情怀：从历史传承到时代担当
                        </h1>
                    </div>

                    <!-- 装饰线 -->
                    <div class="w-48 h-2 bg-gradient-to-r from-[#667eea] via-[#764ba2] to-[#f093fb] my-6 reveal-item rounded-full" data-delay="200"></div>

                    <!-- 副标题 -->
                    <div class="neumorphism-inset p-6 max-w-4xl reveal-item" data-delay="300">
                        <p class="text-xl font-medium tracking-wide text-[#2d3748]">
                            <i class="fas fa-graduation-cap text-[#667eea] mr-3"></i>
                            爱国主义精神、民族自豪感与社会责任感的当代诠释
                            <i class="fas fa-heart text-[#f093fb] ml-3"></i>
                        </p>
                    </div>
                </div>

                <!-- 底部渐变条 -->
                <div class="absolute bottom-0 left-0 w-full h-3 bg-gradient-to-r from-[#667eea] via-[#764ba2] via-[#f093fb] to-[#f5576c] z-20"></div>
            </div>
        </div>

        <!-- 第2页：AI智能目录 -->
        <div class="slide" id="slide2" data-reveal-count="10">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f0f0f3] p-16">
                <!-- 几何装饰背景 -->
                <div class="geometric-bg">
                    <div class="geometric-shape" style="top: 15%; right: 10%; width: 120px; height: 120px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 20px;"></div>
                    <div class="geometric-shape" style="bottom: 15%; left: 15%; width: 80px; height: 80px; background: linear-gradient(45deg, #f093fb, #f5576c); clip-path: polygon(50% 0%, 0% 100%, 100% 100%);"></div>
                </div>

                <!-- AI智能网格背景 -->
                <div class="absolute inset-0 opacity-5 z-0">
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                                <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#667eea" stroke-width="1"/>
                            </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#grid)" />
                    </svg>
                </div>

                <!-- 标题区域 -->
                <header class="flex-shrink-0 w-full mb-12 z-10">
                    <div class="neumorphism p-6 inline-block reveal-item">
                        <div class="flex items-center gap-4">
                            <i class="fas fa-list-ul text-3xl text-[#667eea]"></i>
                            <h1 class="text-5xl font-bold text-[#2d3748]">智能目录导航</h1>
                            <div class="w-3 h-3 bg-[#2ed573] rounded-full animate-pulse"></div>
                        </div>
                    </div>
                    <div class="w-32 h-2 bg-gradient-to-r from-[#667eea] to-[#764ba2] mt-4 rounded-full reveal-item"></div>
                </header>

                <!-- 目录内容 -->
                <main class="flex-grow w-full flex items-center min-h-0 z-10">
                    <div class="w-full grid grid-cols-2 gap-8">
                        <!-- 左列 -->
                        <div class="space-y-4">
                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="100">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center text-white font-bold">1</div>
                                    <span class="text-xl font-medium text-[#2d3748]">家国情怀的内涵解析</span>
                                    <i class="fas fa-chevron-right text-[#667eea] ml-auto"></i>
                                </div>
                            </div>

                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="200">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#764ba2] to-[#f093fb] rounded-full flex items-center justify-center text-white font-bold">2</div>
                                    <span class="text-xl font-medium text-[#2d3748]">历史长河中的家国记忆</span>
                                    <i class="fas fa-chevron-right text-[#764ba2] ml-auto"></i>
                                </div>
                            </div>

                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="300">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f093fb] to-[#f5576c] rounded-full flex items-center justify-center text-white font-bold">3</div>
                                    <span class="text-xl font-medium text-[#2d3748]">民族复兴的辉煌成就</span>
                                    <i class="fas fa-chevron-right text-[#f093fb] ml-auto"></i>
                                </div>
                            </div>

                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300 animate-underline-pulse" data-delay="400">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f5576c] to-[#667eea] rounded-full flex items-center justify-center text-white font-bold">4</div>
                                    <span class="text-xl font-medium text-[#2d3748]">榜样力量：家国担当的践行者</span>
                                    <i class="fas fa-star text-[#f5576c] ml-auto animate-pulse"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 右列 -->
                        <div class="space-y-4">
                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="500">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center text-white font-bold">5</div>
                                    <span class="text-xl font-medium text-[#2d3748]">家国情怀的教育实践</span>
                                    <i class="fas fa-chevron-right text-[#667eea] ml-auto"></i>
                                </div>
                            </div>

                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="600">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#764ba2] to-[#f093fb] rounded-full flex items-center justify-center text-white font-bold">6</div>
                                    <span class="text-xl font-medium text-[#2d3748]">传统节日中的家国情怀</span>
                                    <i class="fas fa-chevron-right text-[#764ba2] ml-auto"></i>
                                </div>
                            </div>

                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="700">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f093fb] to-[#f5576c] rounded-full flex items-center justify-center text-white font-bold">7</div>
                                    <span class="text-xl font-medium text-[#2d3748]">科技领域的家国情怀</span>
                                    <i class="fas fa-chevron-right text-[#f093fb] ml-auto"></i>
                                </div>
                            </div>

                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="800">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f5576c] to-[#667eea] rounded-full flex items-center justify-center text-white font-bold">8</div>
                                    <span class="text-xl font-medium text-[#2d3748]">结语：让家国情怀照亮前行之路</span>
                                    <i class="fas fa-chevron-right text-[#f5576c] ml-auto"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

                <!-- 底部进度指示器 -->
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-10 reveal-item" data-delay="900">
                    <div class="w-3 h-3 bg-[#667eea] rounded-full animate-pulse"></div>
                    <div class="w-3 h-3 bg-[#764ba2] rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                    <div class="w-3 h-3 bg-[#f093fb] rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                    <div class="w-3 h-3 bg-[#f5576c] rounded-full animate-pulse" style="animation-delay: 0.6s;"></div>
                </div>
            </div>
        </div>

        <!-- 第3页：家国情怀的内涵解析 -->
        <div class="slide" id="slide3" data-reveal-count="5">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa] p-16">
                <div class="absolute bottom-8 right-8 text-[120px] font-black text-[#1e90ff]/5 select-none pointer-events-none -rotate-12 z-0 animate-scale-in">
                    <i class="fas fa-landmark"></i>
                </div>
                <header class="flex-shrink-0 w-full mb-10 z-10">
                    <h1 class="text-4xl font-bold text-[#2d3436] reveal-item">一、家国情怀的内涵解析</h1>
                    <div class="w-24 h-1.5 bg-[#1e90ff] mt-4 rounded-full reveal-item"></div>
                </header>
                <main class="flex-grow w-full flex flex-col justify-center min-h-0 z-10">
                    <div class="space-y-8">
                        <div class="flex items-start gap-6 reveal-item" data-delay="100">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#1e90ff]/10 text-[#1e90ff] text-2xl rounded-full flex-shrink-0 mt-1">
                                <i class="fas fa-book-open"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#1e90ff]">核心定义：</strong>个人对家庭和国家共同体的认同与热爱，是爱国主义精神的伦理基础和情感状态。
                            </div>
                        </div>
                        <div class="flex items-start gap-6 reveal-item" data-delay="200">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#2ed573]/10 text-[#2ed573] text-2xl rounded-full flex-shrink-0 mt-1">
                                <i class="fas fa-history"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#2d3436]">历史渊源：</strong>根植于中国氏族血缘宗法制，《礼记》<span class="animate-pulse-highlight">“五止十义”</span>奠定伦理与政治秩序统一的基础。
                            </div>
                        </div>
                        <div class="flex items-start gap-6 reveal-item" data-delay="300">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#ff4757]/10 text-[#ff4757] text-2xl rounded-full flex-shrink-0 mt-1">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#ff4757]">现代升华：</strong>习近平总书记指出
                                <span class="animate-pulse-highlight">“爱国是本分，也是职责”</span>
                                <span class="audio-play-btn" data-audio="https://example.com/audio/xi1.mp3">
                                    <i class="fas fa-volume-up"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- 第4页：历史长河中的家国记忆 -->
        <div class="slide" id="slide4" data-reveal-count="5">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#ff4757] to-[#1e90ff]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 h-full bg-gray-200">
                        <img alt="近代救亡图存浮雕群像，展现近代家国情怀实践场景" class="w-full h-full object-cover object-top animate-scale-in reveal-item" src="https://s.coze.cn/image/Rc08Wze7ztQ/"/>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-6 reveal-item">二、历史长河中的家国记忆</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 reveal-item" data-delay="100">
                                <div class="w-10 h-10 flex items-center justify-center bg-[#1e90ff] text-white rounded-lg flex-shrink-0 mt-1">
                                    <i class="fas fa-landmark"></i>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-[#2d3436] mb-2">古代融合与认同</h2>
                                    <p class="text-[#636e72]">
                                        《礼记》“五止十义”、王昭君与文成公主和亲、“凉州会盟”、土尔扈特东归
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-start gap-4 reveal-item" data-delay="200">
                                <div class="w-10 h-10 flex items-center justify-center bg-[#ff4757] text-white rounded-lg flex-shrink-0 mt-1">
                                    <i class="fas fa-flag"></i>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-[#2d3436] mb-2">近代救亡与抗争</h2>
                                    <p class="text-[#636e72]">
                                        仰韶遗址发现、抗美援朝（<span class="animate-pulse-highlight">宋阿毛绝笔</span>）
                                        <span class="audio-play-btn" data-audio="https://example.com/audio/kangmei.mp3">
                                            <i class="fas fa-volume-up"></i>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第5页：民族复兴的辉煌成就 -->
        <div class="slide" id="slide5" data-reveal-count="5">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full p-12">
                    <div class="w-1/2 flex flex-col justify-center pr-10">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-8 reveal-item">三、民族复兴的辉煌成就</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 reveal-item" data-delay="100">
                                <i class="fas fa-chart-line text-[#1e90ff] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#1e90ff] animate-underline-pulse">经济腾飞</strong>：2023年GDP达<strong class="font-bold animate-pulse-highlight">126万亿元</strong>，较1952年增长<strong class="font-bold animate-pulse-highlight">223倍</strong>。</p>
                            </div>
                            <div class="flex items-start gap-4 reveal-item" data-delay="200">
                                <i class="fas fa-heart-pulse text-[#2ed573] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#2ed573] animate-underline-pulse">民生改善</strong>：农村贫困人口全部脱贫，人均预期寿命提高<strong class="font-bold animate-pulse-highlight">43.6岁</strong>。</p>
                            </div>
                            <div class="flex items-start gap-4 reveal-item" data-delay="300">
                                <i class="fas fa-satellite-dish text-[#ff4757] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#ff4757] animate-underline-pulse">科技强国</strong>："两弹一星"、载人航天、高速铁路等重大科技成就。</p>
                            </div>
                        </div>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center pl-10">
                        <div class="flex-grow relative bg-white rounded-lg shadow-lg p-6 min-h-0 reveal-item" data-delay="400">
                            <canvas id="achievementChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第6页：榜样力量 -->
        <div class="slide" id="slide6" data-reveal-count="5">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#ff4757] to-[#1e90ff]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 h-full bg-gray-200 flex items-center justify-center overflow-hidden">
                        <img alt="陈祥榕戍边英雄军装照，展现军人庄重形象" class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105 reveal-item" src="https://s.coze.cn/image/oaHiEqFHy4s/"/>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-6 reveal-item">四、榜样力量：家国担当的践行者</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 reveal-item" data-delay="100">
                                <i class="fas fa-award text-[#ff4757] mt-1.5 flex-shrink-0"></i>
                                <div>
                                    <h2 class="text-xl font-bold text-[#2d3436] mb-2">英雄烈士</h2>
                                    <p class="text-[#636e72]">陈祥榕（<span class="animate-pulse-highlight">"清澈的爱，只为中国"</span>）
                                        <span class="audio-play-btn" data-audio="https://example.com/audio/chenxiangrong.mp3">
                                            <i class="fas fa-volume-up"></i>
                                        </span>
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-start gap-4 reveal-item" data-delay="200">
                                <i class="fas fa-star text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                <div>
                                    <h2 class="text-xl font-bold text-[#2d3436] mb-2">时代楷模</h2>
                                    <p class="text-[#636e72]"><span class="animate-pulse-highlight">黄令仪</span>（芯片研发）、<span class="animate-pulse-highlight">许振超</span>（"振超效率"）。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第7页：家国情怀的教育实践 -->
        <div class="slide" id="slide7" data-reveal-count="5">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 h-full bg-gray-200">
                        <img alt="高校国防后备连军训队列，展现“行进式”户外实践" class="w-full h-full object-cover object-top reveal-item" src="https://s.coze.cn/image/DAV-MDYxA2o/"/>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-4 reveal-item">五、家国情怀的教育实践</h1>
                        <div class="w-20 h-1 bg-[#1e90ff] mb-8 rounded-full reveal-item"></div>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 reveal-item" data-delay="100">
                                <i class="fas fa-university text-[#1e90ff] mt-1.5 flex-shrink-0 text-xl"></i>
                                <p><strong class="font-bold text-[#2d3436]">高校实践</strong>：<span class="animate-pulse-highlight">河北金融学院</span>“四大课堂”、<span class="animate-pulse-highlight">南开大学</span>“爱国三问”。</p>
                            </div>
                            <div class="flex items-start gap-4 reveal-item" data-delay="200">
                                <i class="fas fa-school text-[#1e90ff] mt-1.5 flex-shrink-0 text-xl"></i>
                                <p><strong class="font-bold text-[#2d3436]">中小学教育</strong>：<span class="animate-pulse-highlight">红色地标打卡计划</span>、<span class="animate-pulse-highlight">宁波初中</span>“情理共生”思政课。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第8页：传统节日中的家国情怀 -->
        <div class="slide" id="slide8" data-reveal-count="6">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa] p-16">
                <div class="absolute bottom-8 right-8 text-[120px] font-black text-[#1e90ff]/5 select-none pointer-events-none -rotate-12 z-0 animate-scale-in">
                    <i class="fas fa-pagelines"></i>
                </div>
                <header class="flex-shrink-0 w-full mb-10 z-10">
                    <h1 class="text-4xl font-bold text-[#2d3436] reveal-item">六、传统节日中的家国情怀</h1>
                    <div class="w-24 h-1.5 bg-[#1e90ff] mt-4 rounded-full reveal-item"></div>
                </header>
                <main class="flex-grow w-full flex flex-col justify-center min-h-0 z-10">
                    <div class="space-y-8">
                        <div class="flex items-start gap-6 reveal-item" data-delay="100">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#1e90ff]/10 text-[#1e90ff] text-2xl rounded-full flex-shrink-0 mt-1">
                                <i class="fas fa-moon"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#1e90ff]">春节</strong>：中华民族最隆重的传统节日，承载辞旧迎新愿望，蕴含家庭团聚伦理观念。
                            </div>
                        </div>
                        <div class="flex items-start gap-6 reveal-item" data-delay="200">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#2ed573]/10 text-[#2ed573] text-2xl rounded-full flex-shrink-0 mt-1">
                                <i class="fas fa-flag"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#2ed573]">端午节</strong>：起源于纪念爱国诗人屈原，赛龙舟、吃粽子等习俗展现团结协作民族精神。
                                <span class="audio-play-btn" data-audio="https://example.com/audio/quyuan.mp3">
                                    <i class="fas fa-volume-up"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex items-start gap-6 reveal-item" data-delay="300">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#ff4757]/10 text-[#ff4757] text-2xl rounded-full flex-shrink-0 mt-1">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#ff4757]">中秋节</strong>：以月之圆兆人之团圆，寄托对家人思念和国家统一渴望，是民族凝聚力象征。
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- 第9页：科技领域的家国情怀 -->
        <div class="slide" id="slide9" data-reveal-count="6">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full p-12">
                    <div class="w-1/2 flex flex-col justify-center pr-10">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-8 reveal-item">七、科技领域的家国情怀</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 reveal-item" data-delay="100">
                                <i class="fas fa-microchip text-[#1e90ff] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#1e90ff] animate-underline-pulse">两弹一星元勋</strong>：钱学森、邓稼先等放弃国外优越条件，回国投身国防科技事业。
                                    <span class="audio-play-btn" data-audio="https://example.com/audio/qianxuesen.mp3">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                </p>
                            </div>
                            <div class="flex items-start gap-4 reveal-item" data-delay="200">
                                <i class="fas fa-rocket text-[#2ed573] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#2ed573] animate-underline-pulse">航天人</strong>：从"东方红一号"到"嫦娥探月"，中国航天人不断刷新中国高度。</p>
                            </div>
                        </div>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center pl-10">
                        <div class="flex-grow relative bg-white rounded-lg shadow-lg p-6 min-h-0 reveal-item" data-delay="300">
                            <canvas id="techAchievementChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第10页：名言警句集锦 -->
        <div class="slide" id="slide10" data-reveal-count="9">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#0d1a26]">
                <!-- 背景优化 -->
                <img alt="水墨风格卷轴背景，营造典雅庄重氛围" class="opacity-40 absolute inset-0 w-full h-full z-0 object-cover object-top animate-scale-in reveal-item" src="https://space.coze.cn/s/X2E_p5psnkY/"/>
                <div class="absolute inset-0 bg-gradient-to-b from-[#0d1a26]/80 via-[#0d1a26]/60 to-[#0d1a26]/90 z-10"></div>

                <!-- 装饰元素 -->
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[900px] h-[900px] bg-radial from-[#1e90ff]/20 to-transparent rounded-full pointer-events-none z-20"></div>
                <div class="absolute top-10 left-10 w-20 h-20 border-2 border-[#ffd700]/30 rounded-full z-20"></div>
                <div class="absolute bottom-10 right-10 w-32 h-32 border-2 border-[#ff4757]/20 rounded-full z-20"></div>

                <div class="relative z-30 flex flex-col items-center justify-start text-center text-white p-8 w-full h-full overflow-y-auto">
                    <header class="flex-shrink-0 mb-6 pt-4">
                        <h1 class="reveal-item text-4xl font-bold tracking-wider text-shadow" style="background: linear-gradient(90deg, #ffffff, #ffd700); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; transition: all 0.5s ease;">
                            家国情怀名言警句集锦
                        </h1>
                        <div class="w-32 h-1 bg-gradient-to-r from-[#ff4757] to-[#1e90ff] mt-3 mx-auto reveal-item"></div>
                    </header>

                    <!-- 名言展示区 -->
                    <main class="flex-grow w-full flex items-start justify-center min-h-0 px-4 pb-20">
                        <div class="w-full max-w-6xl grid grid-cols-2 gap-4 text-base text-white leading-relaxed text-shadow">
                            <!-- 名言1 -->
                            <div class="quote-container reveal-item" data-delay="100">
                                <div class="quote-mark left">"</div>
                                <p class="quote-text">
                                    苟利国家，不求富贵
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-1">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-gray-300 text-sm mt-2 block">——《礼记》</span>
                                </p>
                                <div class="quote-mark right">"</div>
                            </div>

                            <!-- 名言2 -->
                            <div class="quote-container reveal-item" data-delay="200">
                                <div class="quote-mark left">"</div>
                                <p class="quote-text">
                                    位卑未敢忘忧国
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-2">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-gray-300 text-sm mt-2 block">——陆游</span>
                                </p>
                                <div class="quote-mark right">"</div>
                            </div>

                            <!-- 名言3 -->
                            <div class="quote-container reveal-item" data-delay="300">
                                <div class="quote-mark left">"</div>
                                <p class="quote-text">
                                    人生自古谁无死，留取丹心照汗青
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-3">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-gray-300 text-sm mt-2 block">——文天祥</span>
                                </p>
                                <div class="quote-mark right">"</div>
                            </div>

                            <!-- 名言4 -->
                            <div class="quote-container reveal-item" data-delay="400">
                                <div class="quote-mark left">"</div>
                                <p class="quote-text">
                                    先天下之忧而忧，后天下之乐而乐
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-4">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-gray-300 text-sm mt-2 block">——范仲淹</span>
                                </p>
                                <div class="quote-mark right">"</div>
                            </div>

                            <!-- 名言5 -->
                            <div class="quote-container reveal-item" data-delay="500">
                                <div class="quote-mark left">"</div>
                                <p class="quote-text">
                                    实现中华民族伟大复兴，就是中华民族近代以来最伟大的梦想
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-5">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-gray-300 text-sm mt-2 block">——习近平</span>
                                </p>
                                <div class="quote-mark right">"</div>
                            </div>

                            <!-- 名言6 -->
                            <div class="quote-container reveal-item" data-delay="600">
                                <div class="quote-mark left">"</div>
                                <p class="quote-text">
                                    人民对美好生活的向往，就是我们的奋斗目标
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-6">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-gray-300 text-sm mt-2 block">——习近平</span>
                                </p>
                                <div class="quote-mark right">"</div>
                            </div>

                            <!-- 名言7 -->
                            <div class="quote-container reveal-item" data-delay="700">
                                <div class="quote-mark left">"</div>
                                <p class="quote-text">
                                    幸福都是奋斗出来的
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-7">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-gray-300 text-sm mt-2 block">——习近平</span>
                                </p>
                                <div class="quote-mark right">"</div>
                            </div>

                            <!-- 名言8 -->
                            <div class="quote-container reveal-item" data-delay="800">
                                <div class="quote-mark left">"</div>
                                <p class="quote-text">
                                    路漫漫其修远兮，吾将上下而求索
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-8">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-gray-300 text-sm mt-2 block">——屈原</span>
                                </p>
                                <div class="quote-mark right">"</div>
                            </div>

                            <!-- 名言9 -->
                            <div class="quote-container reveal-item" data-delay="900">
                                <div class="quote-mark left">"</div>
                                <p class="quote-text">
                                    为中华之崛起而读书
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-9">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-gray-300 text-sm mt-2 block">——周恩来</span>
                                </p>
                                <div class="quote-mark right">"</div>
                            </div>

                            <!-- 名言10 -->
                            <div class="quote-container reveal-item" data-delay="1000">
                                <div class="quote-mark left">"</div>
                                <p class="quote-text">
                                    我们爱我们的民族，这是我们自信心的泉源
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-10">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-gray-300 text-sm mt-2 block">——周恩来</span>
                                </p>
                                <div class="quote-mark right">"</div>
                            </div>
                        </div>
                    </main>
                </div>
            </div>
        </div>
        <!-- 第11页：结语 -->
        <div class="slide" id="slide11" data-reveal-count="5">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <div class="w-full h-1/2 bg-gray-200">
                    <img alt="长城日出，展现壮丽开阔的希望景象" class="w-full h-full object-cover object-top reveal-item" src="https://s.coze.cn/image/GUO4vtAGxy8/"/>
                </div>
                <div class="w-full h-1/2 flex flex-col justify-center p-16">
                    <header class="mb-6">
                        <h1 class="text-4xl font-bold text-[#2d3436] reveal-item">八、结语：让家国情怀照亮前行之路</h1>
                        <div class="w-24 h-1.5 bg-[#1e90ff] mt-4 rounded-full reveal-item"></div>
                    </header>
                    <main>
                        <p class="text-xl text-[#636e72] leading-relaxed reveal-item" data-delay="100">
                            家国情怀是中华民族的精神纽带，是穿越千年的文化基因，更是新时代奋斗者的精神坐标。
                        </p>
                        <p class="text-xl text-[#636e72] leading-relaxed reveal-item" data-delay="200">
                            从<span class="animate-pulse-highlight">土尔扈特东归</span>的民族大义，到<span class="animate-pulse-highlight">"两弹一星"</span>的科技报国；
                        </p>
                        <p class="text-xl text-[#636e72] leading-relaxed reveal-item" data-delay="300">
                            让我们将个人梦想融入国家发展，共书民族复兴壮丽篇章。
                            <span class="audio-play-btn" data-audio="https://example.com/audio/conclusion.mp3">
                                <i class="fas fa-volume-up"></i>
                            </span>
                        </p>
                    </main>
                </div>
                <div class="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] via-[#2ed573] to-[#ff4757] z-20"></div>
            </div>
        </div>

        <!-- 第12页：致谢 -->
        <div class="slide" id="slide12" data-reveal-count="3">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#0d1a26]">
                <img alt="飘扬的五星红旗旁有多只白鸽飞翔，象征国家繁荣与和平发展" class="absolute inset-0 w-full h-full z-0 opacity-40 object-cover object-top animate-scale-in reveal-item" src="https://s.coze.cn/image/Cc0jNPmyM9U/"/>
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[900px] h-[900px] bg-radial from-[#2ed573]/10 to-transparent rounded-full pointer-events-none z-10"></div>
                <div class="relative z-20 flex flex-col items-center justify-center text-center text-white p-12">
                    <h1 class="text-6xl font-bold tracking-wider text-shadow reveal-item" style="background: linear-gradient(90deg, #ffffff, #ffd700); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        感谢聆听！
                    </h1>
                    <div class="w-32 h-1 bg-[#2ed573] my-8 reveal-item"></div>
                    <p class="text-2xl font-light tracking-widest text-shadow reveal-item" data-delay="200">
                        <span class="animate-pulse-highlight">愿家国情怀永驻心间，激励我们奋勇向前。</span>
                    </p>
                </div>
                <div class="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-[#ff4757] via-[#1e90ff] to-[#2ed573] z-20"></div>
            </div>
        </div>
    </div>

    <!-- 控制栏 -->
    <div class="control-bar">
        <button class="control-btn" id="prevBtn"><i class="fas fa-chevron-left"></i></button>
        <button class="control-btn" id="playPauseBtn"><i class="fas fa-play"></i></button>
        <button class="control-btn" id="nextBtn"><i class="fas fa-chevron-right"></i></button>
        <button class="control-btn audio-btn" id="stopAudioBtn"><i class="fas fa-volume-mute"></i></button>
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        <div class="slide-info" id="slideInfo">第 1 页 / 共 12 页</div>
    </div>

    <script>
        // 全局变量
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        let currentSlide = 0;
        let currentRevealIndex = 0;
        let isPlaying = false;
        let playInterval;
        let audioPlayer = document.getElementById('audioPlayer');
        let currentAudioBtn = null;
        
        // DOM元素
        const slideInfo = document.getElementById('slideInfo');
        const progressBar = document.getElementById('progressBar');
        const progressContainer = document.getElementById('progressContainer');
        const playPauseBtn = document.getElementById('playPauseBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const stopAudioBtn = document.getElementById('stopAudioBtn');

        // 初始化图表
        function initCharts() {
            // 教育成就图表
            const achievementCtx = document.getElementById('achievementChart');
            if (achievementCtx) {
                new Chart(achievementCtx, {
                    type: 'bar',
                    data: {
                        labels: ['学前教育', '义务教育', '高中阶段'],
                        datasets: [
                            {
                                label: '1949年',
                                data: [0.4, 20, 1.1],
                                backgroundColor: 'rgba(255, 99, 132, 0.6)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            },
                            {
                                label: '2023年',
                                data: [91.1, 100, 91.8],
                                backgroundColor: 'rgba(54, 162, 235, 0.6)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'top', labels: { font: { family: "'Noto Sans SC', sans-serif", size: 14 } } },
                            title: { display: true, text: '中国教育普及水平变化（1949-2023）', font: { family: "'Noto Sans SC', sans-serif", size: 18, weight: 'bold' }, color: '#2d3436', padding: { bottom: 20 } }
                        },
                        scales: {
                            y: { beginAtZero: true, title: { display: true, text: '毛入学率 (%)', font: { family: "'Noto Sans SC', sans-serif", size: 14 } }, ticks: { font: { family: "'Noto Sans SC', sans-serif" } } },
                            x: { ticks: { font: { family: "'Noto Sans SC', sans-serif", size: 14 } } }
                        }
                    }
                });
            }

            // 科技成就图表
            const techCtx = document.getElementById('techAchievementChart');
            if (techCtx) {
                new Chart(techCtx, {
                    type: 'line',
                    data: {
                        labels: ['2015', '2017', '2019', '2021', '2023'],
                        datasets: [
                            {
                                label: '中国研发经费投入（万亿元）',
                                data: [1.4, 1.7, 2.2, 2.8, 3.5],
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 2,
                                tension: 0.3,
                                fill: true
                            },
                            {
                                label: '全球创新指数排名',
                                data: [29, 27, 14, 12, 10],
                                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 2,
                                tension: 0.3,
                                fill: true
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'top', labels: { font: { family: "'Noto Sans SC', sans-serif", size: 14 } } },
                            title: { display: true, text: '中国科技发展成就（2015-2023）', font: { family: "'Noto Sans SC', sans-serif", size: 18, weight: 'bold' }, color: '#2d3436', padding: { bottom: 20 } }
                        },
                        scales: {
                            y: { beginAtZero: true, title: { display: true, text: ['研发经费（万亿元）', '创新指数排名'], font: { family: "'Noto Sans SC', sans-serif", size: 14 } }, ticks: { font: { family: "'Noto Sans SC', sans-serif" } } },
                            x: { ticks: { font: { family: "'Noto Sans SC', sans-serif", size: 14 } } }
                        }
                    }
                });
            }
        }

        // 重置当前幻灯片的元素显示状态
        function resetSlideReveal(slideIndex) {
            const slide = slides[slideIndex];
            const revealItems = slide.querySelectorAll('.reveal-item');
            revealItems.forEach(item => {
                item.classList.remove('visible');
            });
        }

        // 显示当前幻灯片的下一个元素 - 性能优化版本
        function revealNextItem() {
            const currentSlideEl = slides[currentSlide];
            const revealItems = currentSlideEl.querySelectorAll('.reveal-item');
            
            // 如果还有未显示的元素，显示下一个
            if (currentRevealIndex < revealItems.length) {
                const item = revealItems[currentRevealIndex];
                const delay = parseInt(item.dataset.delay || 0);
                
                // 使用requestAnimationFrame优化动画性能
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        item.classList.add('visible');
                    }, delay);
                });
                
                currentRevealIndex++;
                return true; // 还有元素未显示
            }
            
            return false; // 所有元素都已显示
        }

        // 显示指定幻灯片 - 性能优化版本
        function showSlide(index) {
            // 停止当前音频
            stopAudio();
            
            // 使用requestAnimationFrame优化动画性能
            requestAnimationFrame(() => {
                // 更新上一张和下一张的样式
                slides.forEach((slide, i) => {
                    slide.classList.remove('active', 'prev', 'next');
                    if (i < index) slide.classList.add('prev');
                    else if (i > index) slide.classList.add('next');
                });
                
                // 隐藏所有幻灯片，显示当前幻灯片
                currentSlide = index;
                slides[currentSlide].classList.add('active');
                
                // 重置当前幻灯片的元素显示状态
                resetSlideReveal(currentSlide);
                currentRevealIndex = 0;
                
                // 更新信息和进度
                updateSlideInfo();
                updateProgress();
                
                // 显示第一个元素，使用较短的延迟
                setTimeout(() => {
                    revealNextItem();
                }, 200);
                
                // 重新初始化当前页图表，使用requestAnimationFrame优化
                if (currentSlide === 4 || currentSlide === 8) {
                    requestAnimationFrame(() => {
                        setTimeout(initCharts, 300);
                    });
                }
            });
        }

        // 更新幻灯片信息
        function updateSlideInfo() {
            slideInfo.textContent = `第 ${currentSlide + 1} 页 / 共 ${totalSlides} 页`;
        }

        // 更新进度条
        function updateProgress() {
            const progress = (currentSlide / (totalSlides - 1)) * 100;
            progressBar.style.width = `${progress}%`;
        }

        // 上一页
        function prevSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(currentSlide);
        }

        // 下一页
        function nextSlide() {
            // 先尝试显示当前页的下一个元素
            if (!revealNextItem()) {
                // 如果所有元素都已显示，则切换到下一页
                currentSlide = (currentSlide + 1) % totalSlides;
                showSlide(currentSlide);
            }
        }

        // 播放/暂停
        function togglePlayPause() {
            if (isPlaying) {
                // 暂停
                clearInterval(playInterval);
                playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            } else {
                // 播放
                playInterval = setInterval(nextSlide, 5000); // 5秒切换一次
                playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            }
            isPlaying = !isPlaying;
        }

        // 播放音频 - 性能优化版本
        function playAudio(audioUrl, btnElement) {
            // 停止当前正在播放的音频
            stopAudio();
            
            // 使用requestAnimationFrame优化UI更新
            requestAnimationFrame(() => {
                // 设置新音频源
                audioPlayer.src = audioUrl;
                
                // 预加载音频
                audioPlayer.preload = 'auto';
                
                // 播放音频
                audioPlayer.play().then(() => {
                    // 更新按钮状态，使用requestAnimationFrame确保UI更新流畅
                    requestAnimationFrame(() => {
                        btnElement.classList.add('playing');
                        btnElement.innerHTML = '<i class="fas fa-volume-up"></i>';
                        currentAudioBtn = btnElement;
                    });
                }).catch(error => {
                    console.error("音频播放失败:", error);
                });
            });
        }

        // 停止音频 - 性能优化版本
        function stopAudio() {
            if (!audioPlayer.paused) {
                audioPlayer.pause();
                audioPlayer.src = '';
                audioPlayer.load(); // 释放资源
            }
            
            // 重置所有音频按钮状态，使用requestAnimationFrame优化UI更新
            if (currentAudioBtn) {
                requestAnimationFrame(() => {
                    currentAudioBtn.classList.remove('playing');
                    currentAudioBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
                    currentAudioBtn = null;
                });
            }
        }

        // 点击进度条跳转 - 性能优化版本
        progressContainer.addEventListener('click', (e) => {
            // 使用防抖处理，避免短时间内多次点击
            if (progressContainer.clickTimeout) {
                clearTimeout(progressContainer.clickTimeout);
            }
            
            progressContainer.clickTimeout = setTimeout(() => {
                const rect = progressContainer.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const percent = x / rect.width;
                currentSlide = Math.floor(percent * (totalSlides - 1));
                showSlide(currentSlide);
            }, 50); // 50毫秒的防抖延迟
        });

        // 点击幻灯片空白处显示下一个元素 - 性能优化版本
        document.querySelector('.ppt-container').addEventListener('click', (e) => {
            // 如果点击的不是音频按钮或控制按钮，则显示下一个元素
            if (!e.target.closest('.audio-play-btn') && !e.target.closest('.control-bar')) {
                // 使用requestAnimationFrame优化UI更新
                requestAnimationFrame(() => {
                    nextSlide();
                });
            }
        });

        // 音频按钮点击事件 - 性能优化版本
        document.querySelectorAll('.audio-play-btn').forEach(btn => {
            // 使用事件委托优化多个按钮的事件处理
            btn.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡，避免触发幻灯片点击
                e.preventDefault(); // 阻止默认行为
                
                // 防止短时间内重复点击
                if (btn.clickLock) return;
                btn.clickLock = true;
                
                const audioUrl = btn.dataset.audio;
                
                // 使用requestAnimationFrame优化UI更新
                requestAnimationFrame(() => {
                    // 如果是当前正在播放的音频，则停止
                    if (btn === currentAudioBtn && !audioPlayer.paused) {
                        stopAudio();
                    } else {
                        // 否则播放新音频
                        playAudio(audioUrl, btn);
                    }
                    
                    // 100毫秒后解除点击锁定
                    setTimeout(() => {
                        btn.clickLock = false;
                    }, 100);
                });
            });
        });

        // 键盘控制 - 性能优化版本
        let keyboardLock = false;
        document.addEventListener('keydown', (e) => {
            // 避免在输入框等元素中触发
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
            
            // 防止短时间内重复触发
            if (keyboardLock) return;
            keyboardLock = true;
            
            // 使用requestAnimationFrame优化UI更新
            requestAnimationFrame(() => {
                switch (e.key) {
                    case 'ArrowLeft':
                        prevSlide();
                        break;
                    case 'ArrowRight':
                    case ' ': // 空格键
                        e.preventDefault();
                        nextSlide();
                        break;
                    case 'p':
                    case 'P':
                        togglePlayPause();
                        break;
                    case 'm':
                    case 'M':
                        stopAudio();
                        break;
                }
                
                // 100毫秒后解除锁定
                setTimeout(() => {
                    keyboardLock = false;
                }, 100);
            });
        });

        // 触摸控制（左右滑动）- 性能优化版本
        let touchStartX = 0;
        let touchStartTime = 0;
        let touchLock = false;
        
        document.addEventListener('touchstart', (e) => {
            // 防止短时间内重复触发
            if (touchLock) return;
            
            touchStartX = e.touches[0].clientX;
            touchStartTime = Date.now();
        }, { passive: true }); // 使用passive选项提高滚动性能

        document.addEventListener('touchend', (e) => {
            // 防止短时间内重复触发
            if (touchLock) return;
            touchLock = true;
            if (!e.changedTouches[0]) {
                touchLock = false;
                return;
            }
            
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndTime = Date.now();
            const diffX = touchStartX - touchEndX;
            const diffTime = touchEndTime - touchStartTime;
            
            // 使用requestAnimationFrame优化UI更新
            requestAnimationFrame(() => {
                // 只有滑动距离和时间在合理范围内才视为有效滑动
                if (diffTime < 500) {
                    if (diffX > 50) {
                        nextSlide(); // 向右滑动，下一页
                    } else if (diffX < -50) {
                        prevSlide(); // 向左滑动，上一页
                    }
                }
                
                // 200毫秒后解除锁定
                setTimeout(() => {
                    touchLock = false;
                }, 200);
            });
        }, { passive: true }); // 使用passive选项提高滚动性能

        // 按钮事件绑定 - 性能优化版本
        // 使用防抖处理避免短时间内多次点击
        let buttonLock = false;
        
        function buttonClickHandler(callback) {
            return function(e) {
                if (buttonLock) return;
                buttonLock = true;
                
                // 使用requestAnimationFrame优化UI更新
                requestAnimationFrame(() => {
                    callback();
                    
                    // 100毫秒后解除锁定
                    setTimeout(() => {
                        buttonLock = false;
                    }, 100);
                });
            };
        }
        
        prevBtn.addEventListener('click', buttonClickHandler(prevSlide));
        nextBtn.addEventListener('click', buttonClickHandler(nextSlide));
        playPauseBtn.addEventListener('click', buttonClickHandler(togglePlayPause));
        stopAudioBtn.addEventListener('click', buttonClickHandler(stopAudio));

        // 音频结束时重置按钮状态 - 性能优化版本
        audioPlayer.addEventListener('ended', () => {
            // 使用requestAnimationFrame优化UI更新
            requestAnimationFrame(() => {
                if (currentAudioBtn) {
                    currentAudioBtn.classList.remove('playing');
                    currentAudioBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
                    currentAudioBtn = null;
                }
            });
        });

        // 初始化
        window.addEventListener('load', () => {
            // 设置所有幻灯片初始状态
            slides.forEach((slide, i) => {
                if (i !== 0) {
                    slide.classList.add(i < 0 ? 'prev' : 'next');
                }
            });
            
            showSlide(0);
            initCharts();
        });
    </script>
</body>
</html>
