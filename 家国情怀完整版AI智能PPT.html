<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>家国情怀：从历史传承到时代担当 - AI智能辅助教学PPT</title>
    <script src="https://unpkg.byted-static.com/coze/space_ppt_lib/1.0.3-alpha.12/lib/tailwindcss.js"></script>
    <script src="https://unpkg.byted-static.com/chart.js/4.5.0/dist/chart.umd.js"></script>
    <script src="https://unpkg.byted-static.com/fortawesome/fontawesome-free/6.7.2/js/all.min.js" data-auto-replace-svg="nest"></script>
    <link href="https://lf-code-agent.coze.cn/obj/x-ai-cn/fonts/google/google-all-fonts.css" rel="stylesheet"/>
    <style>
        .font-noto { font-family: 'Noto Sans SC', sans-serif; }
        body { 
            margin: 0; 
            padding: 0; 
            overflow: hidden; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        /* 新拟物化基础样式 */
        .neumorphism {
            background: #f0f0f3;
            border-radius: 20px;
            box-shadow: 
                20px 20px 60px #bebebe,
                -20px -20px 60px #ffffff;
        }
        
        .neumorphism-inset {
            background: #f0f0f3;
            border-radius: 20px;
            box-shadow: 
                inset 20px 20px 60px #bebebe,
                inset -20px -20px 60px #ffffff;
        }
        
        .neumorphism-dark {
            background: #2d3748;
            border-radius: 20px;
            box-shadow: 
                20px 20px 60px #1a202c,
                -20px -20px 60px #404a5c;
        }
        
        /* 玻璃拟态效果 */
        .glassmorphism {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }
        
        .glassmorphism-dark {
            background: rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        /* 粘土拟态效果 */
        .claymorphism {
            background: linear-gradient(145deg, #f0f0f3, #cacaca);
            border-radius: 30px;
            box-shadow: 
                25px 25px 50px #a8a8a8,
                -25px -25px 50px #ffffff,
                inset 5px 5px 10px rgba(255, 255, 255, 0.5),
                inset -5px -5px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .claymorphism::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent);
            border-radius: 30px;
            pointer-events: none;
        }
        
        .claymorphism-colorful {
            background: linear-gradient(145deg, #ff9a9e, #fecfef, #fecfef, #ff9a9e);
            border-radius: 30px;
            box-shadow: 
                25px 25px 50px rgba(255, 154, 158, 0.3),
                -25px -25px 50px rgba(254, 207, 239, 0.3),
                inset 5px 5px 10px rgba(255, 255, 255, 0.5),
                inset -5px -5px 10px rgba(255, 154, 158, 0.2);
        }
        
        /* 几何装饰元素 */
        .geometric-bg {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .geometric-shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        .geometric-shape:nth-child(1) {
            top: 10%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 50%;
            animation-delay: 0s;
        }
        
        .geometric-shape:nth-child(2) {
            top: 20%;
            right: 15%;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #48dbfb, #0abde3);
            transform: rotate(45deg);
            animation-delay: 2s;
        }
        
        .geometric-shape:nth-child(3) {
            bottom: 20%;
            left: 20%;
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #ff9ff3, #f368e0);
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        /* AI粒子效果 */
        .ai-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(102, 126, 234, 0.6);
            border-radius: 50%;
            animation: particleFloat 8s linear infinite;
        }
        
        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }
        
        /* PPT容器样式 */
        .ppt-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 单页幻灯片样式 */
        .slide {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transition: all 1.2s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            transform: scale(0.95) translateY(30px);
            filter: blur(3px);
        }
        .slide.active {
            opacity: 1;
            z-index: 10;
            transform: scale(1) translateY(0);
            filter: blur(0);
        }
        .slide.next {
            transform: translateX(100%) scale(0.9);
            filter: blur(5px);
        }
        .slide.prev {
            transform: translateX(-100%) scale(0.9);
            filter: blur(5px);
        }
        
        /* AI智能控制栏样式 */
        .control-bar {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 15px 25px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 30px;
            max-width: 900px;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .control-bar:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateX(-50%) translateY(-3px);
            box-shadow: 
                0 15px 45px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
        
        /* 新拟物化按钮 */
        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(145deg, #f0f0f3, #cacaca);
            color: #667eea;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 
                8px 8px 16px #bebebe,
                -8px -8px 16px #ffffff;
            position: relative;
            overflow: hidden;
        }
        
        .control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(145deg, transparent, rgba(255, 255, 255, 0.1));
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 
                12px 12px 24px #bebebe,
                -12px -12px 24px #ffffff;
            color: #5a67d8;
        }
        
        .control-btn:hover::before {
            opacity: 1;
        }
        
        .control-btn:active {
            transform: translateY(0);
            box-shadow: 
                inset 8px 8px 16px #bebebe,
                inset -8px -8px 16px #ffffff;
        }
        
        .control-btn.audio-btn {
            background: linear-gradient(145deg, #ff6b6b, #ee5a52);
            color: white;
            box-shadow: 
                8px 8px 16px #d63031,
                -8px -8px 16px #ff7675;
        }
        
        .control-btn.audio-btn:hover {
            background: linear-gradient(145deg, #ee5a52, #d63031);
            box-shadow: 
                12px 12px 24px #d63031,
                -12px -12px 24px #ff7675;
        }
        
        /* 进度条新拟物化设计 */
        .progress-container {
            flex: 1;
            height: 8px;
            background: #f0f0f3;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            box-shadow: 
                inset 4px 4px 8px #bebebe,
                inset -4px -4px 8px #ffffff;
            position: relative;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
            position: relative;
        }
        
        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
            border-radius: 0 10px 10px 0;
        }
        
        .slide-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-family: 'Noto Sans SC', sans-serif;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* AI智能逐点显示元素样式 */
        .reveal-item {
            opacity: 0;
            transform: translateY(40px) scale(0.9);
            transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
            filter: blur(2px);
            position: relative;
        }

        .reveal-item.visible {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0);
        }

        .reveal-item::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
            border-radius: 10px;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
            filter: blur(10px);
        }

        .reveal-item:hover::before {
            opacity: 0.3;
        }

        /* AI音频播放按钮新拟物化设计 */
        .audio-play-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(145deg, #ff6b6b, #ee5a52);
            color: white;
            cursor: pointer;
            margin-left: 10px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            vertical-align: middle;
            box-shadow:
                6px 6px 12px rgba(238, 90, 82, 0.3),
                -6px -6px 12px rgba(255, 107, 107, 0.3);
            position: relative;
            overflow: hidden;
        }

        .audio-play-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(145deg, transparent, rgba(255, 255, 255, 0.2));
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .audio-play-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow:
                8px 8px 16px rgba(238, 90, 82, 0.4),
                -8px -8px 16px rgba(255, 107, 107, 0.4);
        }

        .audio-play-btn:hover::before {
            opacity: 1;
        }

        .audio-play-btn.playing {
            animation: aiPulse 2s infinite;
        }

        @keyframes aiPulse {
            0% {
                transform: scale(1);
                box-shadow:
                    6px 6px 12px rgba(238, 90, 82, 0.3),
                    -6px -6px 12px rgba(255, 107, 107, 0.3),
                    0 0 0 0 rgba(255, 107, 107, 0.7);
            }
            50% {
                transform: scale(1.1);
                box-shadow:
                    8px 8px 16px rgba(238, 90, 82, 0.4),
                    -8px -8px 16px rgba(255, 107, 107, 0.4),
                    0 0 0 15px rgba(255, 107, 107, 0);
            }
            100% {
                transform: scale(1);
                box-shadow:
                    6px 6px 12px rgba(238, 90, 82, 0.3),
                    -6px -6px 12px rgba(255, 107, 107, 0.3),
                    0 0 0 0 rgba(255, 107, 107, 0);
            }
        }

        /* AI智能动画样式 */
        @keyframes aiGlow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 40px rgba(102, 126, 234, 0.6);
                transform: scale(1.02);
            }
        }

        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(30px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            0% { transform: translateY(60px) scale(0.95); opacity: 0; filter: blur(3px); }
            100% { transform: translateY(0) scale(1); opacity: 1; filter: blur(0); }
        }

        @keyframes scaleIn {
            0% { transform: scale(0.8) rotate(-5deg); opacity: 0; filter: blur(5px); }
            100% { transform: scale(1) rotate(0deg); opacity: 1; filter: blur(0); }
        }

        @keyframes pulseHighlight {
            0% {
                background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.2));
                transform: scale(1);
            }
            50% {
                background: linear-gradient(45deg, rgba(255, 215, 0, 0.4), rgba(255, 193, 7, 0.4));
                transform: scale(1.05);
            }
            100% {
                background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.2));
                transform: scale(1);
            }
        }

        @keyframes underlinePulse {
            0% {
                text-decoration-thickness: 2px;
                opacity: 1;
                text-shadow: 0 0 10px rgba(255, 71, 87, 0.3);
            }
            50% {
                text-decoration-thickness: 4px;
                opacity: 0.8;
                text-shadow: 0 0 20px rgba(255, 71, 87, 0.6);
            }
            100% {
                text-decoration-thickness: 2px;
                opacity: 1;
                text-shadow: 0 0 10px rgba(255, 71, 87, 0.3);
            }
        }

        @keyframes morphingGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .animate-fade-in { animation: fadeIn 1s cubic-bezier(0.4, 0, 0.2, 1) forwards; }
        .animate-slide-up { animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1) forwards; }
        .animate-scale-in { animation: scaleIn 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards; }
        .animate-pulse-highlight {
            animation: pulseHighlight 3s infinite;
            padding: 2px 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .animate-underline-pulse {
            animation: underlinePulse 2s infinite;
            text-decoration: underline;
            text-decoration-color: #ff4757;
            position: relative;
        }

        .ai-glow { animation: aiGlow 4s ease-in-out infinite; }

        .text-shadow {
            text-shadow:
                0 2px 10px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(102, 126, 234, 0.2);
        }

        .bg-radial { background-image: radial-gradient(circle, var(--tw-gradient-stops)); }

        .morphing-gradient {
            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 400% 400%;
            animation: morphingGradient 8s ease infinite;
        }
    </style>
</head>
<body class="font-noto">
    <!-- 音频元素（隐藏） -->
    <audio id="audioPlayer" preload="none">
        <source src="" type="audio/mpeg">
    </audio>

    <!-- PPT容器 -->
    <div class="ppt-container" id="pptContainer">
        <!-- 第1页：AI智能封面 -->
        <div class="slide active" id="slide1" data-reveal-count="5">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden morphing-gradient">
                <!-- AI粒子效果 -->
                <div class="ai-particles">
                    <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
                    <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
                    <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
                    <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
                    <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
                    <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
                    <div class="particle" style="left: 70%; animation-delay: 6s;"></div>
                    <div class="particle" style="left: 80%; animation-delay: 7s;"></div>
                </div>

                <!-- 几何装饰背景 -->
                <div class="geometric-bg">
                    <div class="geometric-shape"></div>
                    <div class="geometric-shape"></div>
                    <div class="geometric-shape"></div>
                </div>

                <!-- 主背景图片 -->
                <img alt="蜿蜒于山峦间的长城剪影，展现历史厚重感" class="absolute inset-0 w-full h-full z-0 opacity-30 object-cover object-top animate-scale-in" src="https://s.coze.cn/image/mRjvTG5ZzMM/"/>

                <!-- AI光环效果 -->
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-radial from-[#667eea]/20 via-[#764ba2]/10 to-transparent rounded-full pointer-events-none z-10 ai-glow"></div>
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-radial from-[#f093fb]/15 to-transparent rounded-full pointer-events-none z-10 ai-glow" style="animation-delay: 2s;"></div>

                <!-- 玻璃拟态内容容器 -->
                <div class="relative z-20 flex flex-col items-center justify-center text-center p-12">
                    <!-- AI智能标识 -->
                    <div class="glassmorphism-dark px-6 py-3 mb-8 reveal-item">
                        <div class="flex items-center gap-3 text-white">
                            <i class="fas fa-brain text-[#667eea] text-xl animate-pulse"></i>
                            <span class="text-sm font-medium tracking-wider">AI智能辅助教学系统</span>
                            <div class="w-2 h-2 bg-[#2ed573] rounded-full animate-pulse"></div>
                        </div>
                    </div>

                    <!-- 主标题 -->
                    <div class="claymorphism p-8 mb-8 reveal-item" data-delay="100">
                        <h1 class="text-5xl font-black tracking-wider text-shadow" style="background: linear-gradient(135deg, #667eea, #764ba2, #f093fb); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                            家国情怀：从历史传承到时代担当
                        </h1>
                    </div>

                    <!-- 装饰线 -->
                    <div class="w-48 h-2 bg-gradient-to-r from-[#667eea] via-[#764ba2] to-[#f093fb] my-6 reveal-item rounded-full" data-delay="200"></div>

                    <!-- 副标题 -->
                    <div class="glassmorphism p-6 max-w-4xl reveal-item" data-delay="300">
                        <p class="text-xl font-medium tracking-wide text-white">
                            <i class="fas fa-graduation-cap text-[#667eea] mr-3"></i>
                            爱国主义精神、民族自豪感与社会责任感的当代诠释
                            <i class="fas fa-heart text-[#f093fb] ml-3"></i>
                        </p>
                    </div>

                    <!-- AI助教提示 -->
                    <div class="claymorphism-colorful p-4 mt-6 reveal-item" data-delay="400">
                        <div class="flex items-center gap-3 text-white">
                            <i class="fas fa-robot text-xl"></i>
                            <span class="text-sm">AI助教将为您提供智能化学习体验</span>
                            <i class="fas fa-sparkles text-yellow-300 animate-pulse"></i>
                        </div>
                    </div>
                </div>

                <!-- 底部渐变条 -->
                <div class="absolute bottom-0 left-0 w-full h-3 bg-gradient-to-r from-[#667eea] via-[#764ba2] via-[#f093fb] to-[#f5576c] z-20"></div>
            </div>
        </div>

        <!-- 第2页：AI智能目录导航 -->
        <div class="slide" id="slide2" data-reveal-count="12">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f0f0f3] p-16">
                <!-- AI粒子效果 -->
                <div class="ai-particles">
                    <div class="particle" style="left: 15%; animation-delay: 0s; background: rgba(118, 75, 162, 0.6);"></div>
                    <div class="particle" style="left: 35%; animation-delay: 2s; background: rgba(240, 147, 251, 0.6);"></div>
                    <div class="particle" style="left: 55%; animation-delay: 4s; background: rgba(245, 87, 108, 0.6);"></div>
                    <div class="particle" style="left: 75%; animation-delay: 6s; background: rgba(102, 126, 234, 0.6);"></div>
                </div>

                <!-- 几何装饰背景 -->
                <div class="geometric-bg">
                    <div class="geometric-shape" style="top: 15%; right: 10%; width: 120px; height: 120px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 20px;"></div>
                    <div class="geometric-shape" style="bottom: 15%; left: 15%; width: 80px; height: 80px; background: linear-gradient(45deg, #f093fb, #f5576c); clip-path: polygon(50% 0%, 0% 100%, 100% 100%);"></div>
                </div>

                <!-- AI智能网格背景 -->
                <div class="absolute inset-0 opacity-5 z-0">
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                                <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#667eea" stroke-width="1"/>
                            </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#grid)" />
                    </svg>
                </div>

                <!-- 标题区域 -->
                <header class="flex-shrink-0 w-full mb-12 z-10">
                    <div class="claymorphism p-6 inline-block reveal-item">
                        <div class="flex items-center gap-4">
                            <i class="fas fa-list-ul text-3xl text-[#667eea]"></i>
                            <h1 class="text-5xl font-bold text-[#2d3748]">AI智能目录导航</h1>
                            <div class="w-3 h-3 bg-[#2ed573] rounded-full animate-pulse"></div>
                        </div>
                    </div>
                    <div class="w-32 h-2 bg-gradient-to-r from-[#667eea] to-[#764ba2] mt-4 rounded-full reveal-item"></div>
                </header>

                <!-- 目录内容 -->
                <main class="flex-grow w-full flex items-center min-h-0 z-10">
                    <div class="w-full grid grid-cols-2 gap-8">
                        <!-- 左列 -->
                        <div class="space-y-4">
                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="100">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center text-white font-bold">1</div>
                                    <span class="text-xl font-medium text-[#2d3748]">家国情怀的内涵解析</span>
                                    <i class="fas fa-chevron-right text-[#667eea] ml-auto"></i>
                                </div>
                            </div>

                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="200">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#764ba2] to-[#f093fb] rounded-full flex items-center justify-center text-white font-bold">2</div>
                                    <span class="text-xl font-medium text-[#2d3748]">历史长河中的家国记忆</span>
                                    <i class="fas fa-chevron-right text-[#764ba2] ml-auto"></i>
                                </div>
                            </div>

                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="300">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f093fb] to-[#f5576c] rounded-full flex items-center justify-center text-white font-bold">3</div>
                                    <span class="text-xl font-medium text-[#2d3748]">民族复兴的辉煌成就</span>
                                    <i class="fas fa-chevron-right text-[#f093fb] ml-auto"></i>
                                </div>
                            </div>

                            <div class="claymorphism-colorful p-4 reveal-item hover:scale-105 transition-transform duration-300 animate-underline-pulse" data-delay="400">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f5576c] to-[#667eea] rounded-full flex items-center justify-center text-white font-bold">4</div>
                                    <span class="text-xl font-medium text-white">榜样力量：家国担当的践行者</span>
                                    <i class="fas fa-star text-[#ffd700] ml-auto animate-pulse"></i>
                                </div>
                            </div>

                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="500">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center text-white font-bold">5</div>
                                    <span class="text-xl font-medium text-[#2d3748]">家国情怀的教育实践</span>
                                    <i class="fas fa-chevron-right text-[#667eea] ml-auto"></i>
                                </div>
                            </div>

                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="600">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#764ba2] to-[#f093fb] rounded-full flex items-center justify-center text-white font-bold">6</div>
                                    <span class="text-xl font-medium text-[#2d3748]">传统节日中的家国情怀</span>
                                    <i class="fas fa-chevron-right text-[#764ba2] ml-auto"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 右列 -->
                        <div class="space-y-4">
                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="700">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f093fb] to-[#f5576c] rounded-full flex items-center justify-center text-white font-bold">7</div>
                                    <span class="text-xl font-medium text-[#2d3748]">科技领域的家国情怀</span>
                                    <i class="fas fa-chevron-right text-[#f093fb] ml-auto"></i>
                                </div>
                            </div>

                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="800">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f5576c] to-[#667eea] rounded-full flex items-center justify-center text-white font-bold">8</div>
                                    <span class="text-xl font-medium text-[#2d3748]">新时代青年的使命担当</span>
                                    <i class="fas fa-chevron-right text-[#f5576c] ml-auto"></i>
                                </div>
                            </div>

                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="900">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center text-white font-bold">9</div>
                                    <span class="text-xl font-medium text-[#2d3748]">家国情怀的国际视野</span>
                                    <i class="fas fa-chevron-right text-[#667eea] ml-auto"></i>
                                </div>
                            </div>

                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="1000">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#764ba2] to-[#f093fb] rounded-full flex items-center justify-center text-white font-bold">10</div>
                                    <span class="text-xl font-medium text-[#2d3748]">名言警句集锦</span>
                                    <i class="fas fa-chevron-right text-[#764ba2] ml-auto"></i>
                                </div>
                            </div>

                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="1100">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f093fb] to-[#f5576c] rounded-full flex items-center justify-center text-white font-bold">11</div>
                                    <span class="text-xl font-medium text-[#2d3748]">结语：让家国情怀照亮前行之路</span>
                                    <i class="fas fa-chevron-right text-[#f093fb] ml-auto"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

                <!-- AI助教提示 -->
                <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2 glassmorphism-dark p-3 reveal-item" data-delay="1200">
                    <div class="flex items-center gap-2 text-white text-sm">
                        <i class="fas fa-robot text-[#667eea]"></i>
                        <span>AI助教提示：点击任意章节可快速跳转</span>
                        <i class="fas fa-mouse-pointer text-[#f093fb] animate-pulse"></i>
                    </div>
                </div>

                <!-- 底部进度指示器 -->
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-10 reveal-item" data-delay="1300">
                    <div class="w-3 h-3 bg-[#667eea] rounded-full animate-pulse"></div>
                    <div class="w-3 h-3 bg-[#764ba2] rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                    <div class="w-3 h-3 bg-[#f093fb] rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                    <div class="w-3 h-3 bg-[#f5576c] rounded-full animate-pulse" style="animation-delay: 0.6s;"></div>
                </div>
            </div>
        </div>

        <!-- 第3页：家国情怀的内涵解析 -->
        <div class="slide" id="slide3" data-reveal-count="7">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f0f0f3] p-16">
                <!-- AI粒子效果 -->
                <div class="ai-particles">
                    <div class="particle" style="left: 20%; animation-delay: 1s; background: rgba(102, 126, 234, 0.4);"></div>
                    <div class="particle" style="left: 60%; animation-delay: 3s; background: rgba(118, 75, 162, 0.4);"></div>
                    <div class="particle" style="left: 80%; animation-delay: 5s; background: rgba(240, 147, 251, 0.4);"></div>
                </div>

                <!-- 几何装饰背景 -->
                <div class="geometric-bg">
                    <div class="geometric-shape" style="bottom: 10%; right: 10%; width: 150px; height: 150px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 50%; opacity: 0.05;"></div>
                </div>

                <!-- AI智能图标装饰 -->
                <div class="absolute bottom-8 right-8 text-[120px] font-black text-[#667eea]/8 select-none pointer-events-none -rotate-12 z-0 animate-scale-in">
                    <i class="fas fa-brain"></i>
                </div>

                <!-- 标题区域 -->
                <header class="flex-shrink-0 w-full mb-10 z-10">
                    <div class="claymorphism p-6 inline-block reveal-item">
                        <div class="flex items-center gap-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center text-white">
                                <i class="fas fa-lightbulb text-xl"></i>
                            </div>
                            <h1 class="text-4xl font-bold text-[#2d3748]">一、家国情怀的内涵解析</h1>
                        </div>
                    </div>
                    <div class="w-32 h-2 bg-gradient-to-r from-[#667eea] to-[#764ba2] mt-4 rounded-full reveal-item"></div>
                </header>

                <!-- 内容区域 -->
                <main class="flex-grow w-full flex flex-col justify-center min-h-0 z-10">
                    <div class="space-y-6">
                        <!-- 核心定义 -->
                        <div class="glassmorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="100">
                            <div class="flex items-start gap-6">
                                <div class="claymorphism w-16 h-16 flex items-center justify-center text-[#667eea] text-2xl rounded-full flex-shrink-0">
                                    <i class="fas fa-book-open"></i>
                                </div>
                                <div class="text-xl text-[#2d3748] leading-relaxed">
                                    <strong class="font-bold text-[#667eea] text-2xl">核心定义：</strong>
                                    <br>
                                    <span class="text-lg">个人对家庭和国家共同体的认同与热爱，是爱国主义精神的伦理基础和情感状态。体现了中华民族"修身、齐家、治国、平天下"的价值追求。</span>
                                </div>
                            </div>
                        </div>

                        <!-- 历史渊源 -->
                        <div class="glassmorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="200">
                            <div class="flex items-start gap-6">
                                <div class="claymorphism w-16 h-16 flex items-center justify-center text-[#764ba2] text-2xl rounded-full flex-shrink-0">
                                    <i class="fas fa-history"></i>
                                </div>
                                <div class="text-xl text-[#2d3748] leading-relaxed">
                                    <strong class="font-bold text-[#764ba2] text-2xl">历史渊源：</strong>
                                    <br>
                                    <span class="text-lg">根植于中国氏族血缘宗法制，《礼记》<span class="animate-pulse-highlight">"五止十义"</span>奠定伦理与政治秩序统一的基础。从孔子的"仁者爱人"到孟子的"民为贵"，构建了家国一体的思想体系。</span>
                                </div>
                            </div>
                        </div>

                        <!-- 现代升华 -->
                        <div class="glassmorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="300">
                            <div class="flex items-start gap-6">
                                <div class="claymorphism w-16 h-16 flex items-center justify-center text-[#f093fb] text-2xl rounded-full flex-shrink-0">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <div class="text-xl text-[#2d3748] leading-relaxed">
                                    <strong class="font-bold text-[#f093fb] text-2xl">现代升华：</strong>
                                    <br>
                                    <span class="text-lg">习近平总书记指出<span class="animate-pulse-highlight">"爱国是本分，也是职责"</span>，强调要把爱国情、强国志、报国行自觉融入坚持和发展中国特色社会主义事业中。</span>
                                    <span class="audio-play-btn" data-audio="custom-audio-xi">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- 时代特征 -->
                        <div class="glassmorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="400">
                            <div class="flex items-start gap-6">
                                <div class="claymorphism w-16 h-16 flex items-center justify-center text-[#f5576c] text-2xl rounded-full flex-shrink-0">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="text-xl text-[#2d3748] leading-relaxed">
                                    <strong class="font-bold text-[#f5576c] text-2xl">时代特征：</strong>
                                    <br>
                                    <span class="text-lg">新时代家国情怀具有<span class="animate-pulse-highlight">全球视野</span>、<span class="animate-pulse-highlight">创新精神</span>、<span class="animate-pulse-highlight">责任担当</span>的鲜明特色，体现了中华民族伟大复兴的时代要求。</span>
                                </div>
                            </div>
                        </div>

                        <!-- AI智能总结 -->
                        <div class="glassmorphism-dark p-6 reveal-item" data-delay="500">
                            <div class="flex items-center gap-4 text-white">
                                <i class="fas fa-robot text-2xl text-[#667eea] animate-pulse"></i>
                                <div>
                                    <h3 class="text-lg font-bold mb-2 flex items-center gap-2">
                                        AI智能总结
                                        <i class="fas fa-sparkles text-yellow-300 text-sm"></i>
                                    </h3>
                                    <p class="text-base opacity-90">家国情怀体现了个人与集体、传统与现代、情感与理性的有机统一，是中华民族精神的重要组成部分，在新时代焕发出更加强烈的生命力和感召力。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- 第4页：历史长河中的家国记忆 -->
        <div class="slide" id="slide4" data-reveal-count="8">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f0f0f3]">
                <!-- AI粒子效果 -->
                <div class="ai-particles">
                    <div class="particle" style="left: 25%; animation-delay: 0s; background: rgba(245, 87, 108, 0.5);"></div>
                    <div class="particle" style="left: 45%; animation-delay: 2s; background: rgba(102, 126, 234, 0.5);"></div>
                    <div class="particle" style="left: 65%; animation-delay: 4s; background: rgba(118, 75, 162, 0.5);"></div>
                </div>

                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#ff4757] to-[#1e90ff]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 h-full bg-gray-200 relative overflow-hidden">
                        <img alt="近代救亡图存浮雕群像，展现近代家国情怀实践场景" class="w-full h-full object-cover object-top animate-scale-in reveal-item" src="https://s.coze.cn/image/Rc08Wze7ztQ/"/>
                        <!-- 玻璃拟态覆盖层 -->
                        <div class="absolute inset-0 glassmorphism-dark opacity-20"></div>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center p-16 relative">
                        <!-- 标题 -->
                        <div class="claymorphism p-6 mb-8 reveal-item">
                            <h1 class="text-4xl font-bold text-[#2d3748] flex items-center gap-3">
                                <i class="fas fa-scroll text-[#667eea]"></i>
                                二、历史长河中的家国记忆
                            </h1>
                        </div>

                        <div class="space-y-6 text-lg text-[#2d3748] leading-relaxed">
                            <!-- 古代融合与认同 -->
                            <div class="glassmorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="100">
                                <div class="flex items-start gap-4">
                                    <div class="claymorphism w-12 h-12 flex items-center justify-center text-[#667eea] rounded-lg flex-shrink-0 mt-1">
                                        <i class="fas fa-landmark"></i>
                                    </div>
                                    <div>
                                        <h2 class="text-xl font-bold text-[#2d3748] mb-2 flex items-center gap-2">
                                            古代融合与认同
                                            <i class="fas fa-yin-yang text-[#667eea] text-sm"></i>
                                        </h2>
                                        <p class="text-[#636e72]">
                                            《礼记》"五止十义"奠定家国伦理基础，王昭君与文成公主和亲促进民族融合，"凉州会盟"体现多元一体，土尔扈特东归展现<span class="animate-pulse-highlight">民族向心力</span>。
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- 近代救亡与抗争 -->
                            <div class="glassmorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="200">
                                <div class="flex items-start gap-4">
                                    <div class="claymorphism w-12 h-12 flex items-center justify-center text-[#f5576c] rounded-lg flex-shrink-0 mt-1">
                                        <i class="fas fa-flag"></i>
                                    </div>
                                    <div>
                                        <h2 class="text-xl font-bold text-[#2d3748] mb-2 flex items-center gap-2">
                                            近代救亡与抗争
                                            <i class="fas fa-fire text-[#f5576c] text-sm"></i>
                                        </h2>
                                        <p class="text-[#636e72]">
                                            从洋务运动到戊戌变法，从辛亥革命到五四运动，无数仁人志士以<span class="animate-pulse-highlight">"天下兴亡，匹夫有责"</span>的担当精神，谱写了救亡图存的壮丽篇章。
                                            <span class="audio-play-btn" data-audio="custom-audio-jiuwang">
                                                <i class="fas fa-volume-up"></i>
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- 现代建设与发展 -->
                            <div class="glassmorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="300">
                                <div class="flex items-start gap-4">
                                    <div class="claymorphism w-12 h-12 flex items-center justify-center text-[#2ed573] rounded-lg flex-shrink-0 mt-1">
                                        <i class="fas fa-rocket"></i>
                                    </div>
                                    <div>
                                        <h2 class="text-xl font-bold text-[#2d3748] mb-2 flex items-center gap-2">
                                            现代建设与发展
                                            <i class="fas fa-star text-[#2ed573] text-sm"></i>
                                        </h2>
                                        <p class="text-[#636e72]">
                                            新中国成立以来，从"两弹一星"到载人航天，从脱贫攻坚到全面小康，中华儿女用<span class="animate-pulse-highlight">实际行动</span>诠释着新时代的家国情怀。
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI历史分析 -->
                        <div class="glassmorphism-dark p-4 mt-6 reveal-item" data-delay="400">
                            <div class="flex items-center gap-3 text-white text-sm">
                                <i class="fas fa-chart-line text-[#667eea]"></i>
                                <span>AI历史分析：家国情怀在不同历史时期呈现出不同特点，但爱国主义精神始终是其核心内涵</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第5页：民族复兴的辉煌成就 -->
        <div class="slide" id="slide5" data-reveal-count="6">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f0f0f3]">
                <!-- AI粒子效果 -->
                <div class="ai-particles">
                    <div class="particle" style="left: 30%; animation-delay: 1s; background: rgba(46, 213, 115, 0.6);"></div>
                    <div class="particle" style="left: 70%; animation-delay: 3s; background: rgba(102, 126, 234, 0.6);"></div>
                </div>

                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full p-12">
                    <div class="w-1/2 flex flex-col justify-center pr-10">
                        <div class="claymorphism p-6 mb-8 reveal-item">
                            <h1 class="text-4xl font-bold text-[#2d3748] flex items-center gap-3">
                                <i class="fas fa-chart-line text-[#2ed573]"></i>
                                三、民族复兴的辉煌成就
                            </h1>
                        </div>

                        <div class="space-y-6 text-lg text-[#2d3748] leading-relaxed">
                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="100">
                                <div class="flex items-start gap-4">
                                    <i class="fas fa-chart-line text-[#1e90ff] text-2xl mt-1 flex-shrink-0"></i>
                                    <p><strong class="font-bold text-[#1e90ff] animate-underline-pulse">经济腾飞</strong>：2023年GDP达<span class="animate-pulse-highlight">126万亿元</span>，较1952年增长<span class="animate-pulse-highlight">223倍</span>，成为世界第二大经济体。</p>
                                </div>
                            </div>

                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="200">
                                <div class="flex items-start gap-4">
                                    <i class="fas fa-heart-pulse text-[#2ed573] text-2xl mt-1 flex-shrink-0"></i>
                                    <p><strong class="font-bold text-[#2ed573] animate-underline-pulse">民生改善</strong>：农村贫困人口全部脱贫，人均预期寿命提高<span class="animate-pulse-highlight">43.6岁</span>，全面建成小康社会。</p>
                                </div>
                            </div>

                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="300">
                                <div class="flex items-start gap-4">
                                    <i class="fas fa-satellite-dish text-[#ff4757] text-2xl mt-1 flex-shrink-0"></i>
                                    <p><strong class="font-bold text-[#ff4757] animate-underline-pulse">科技强国</strong>："两弹一星"、载人航天、高速铁路、5G通信等重大科技成就举世瞩目。</p>
                                </div>
                            </div>

                            <div class="glassmorphism p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="400">
                                <div class="flex items-start gap-4">
                                    <i class="fas fa-globe text-[#764ba2] text-2xl mt-1 flex-shrink-0"></i>
                                    <p><strong class="font-bold text-[#764ba2] animate-underline-pulse">国际地位</strong>：从"站起来"到"富起来"再到"强起来"，中国日益走近世界舞台中央。</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="w-1/2 flex flex-col justify-center pl-10">
                        <div class="glassmorphism p-6 min-h-0 reveal-item" data-delay="500">
                            <h3 class="text-xl font-bold text-[#2d3748] mb-4 text-center">中国发展成就数据图表</h3>
                            <canvas id="achievementChart" class="w-full h-80"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第6页：榜样力量 -->
        <div class="slide" id="slide6" data-reveal-count="7">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f0f0f3]">
                <!-- AI粒子效果 -->
                <div class="ai-particles">
                    <div class="particle" style="left: 20%; animation-delay: 0s; background: rgba(255, 71, 87, 0.6);"></div>
                    <div class="particle" style="left: 80%; animation-delay: 4s; background: rgba(102, 126, 234, 0.6);"></div>
                </div>

                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#ff4757] to-[#1e90ff]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 h-full bg-gray-200 flex items-center justify-center overflow-hidden relative">
                        <img alt="陈祥榕戍边英雄军装照，展现军人庄重形象" class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105 reveal-item" src="https://s.coze.cn/image/oaHiEqFHy4s/"/>
                        <!-- 玻璃拟态覆盖层 -->
                        <div class="absolute inset-0 glassmorphism-dark opacity-10"></div>
                        <!-- 英雄标识 -->
                        <div class="absolute top-4 left-4 glassmorphism-dark p-2 rounded-lg reveal-item" data-delay="100">
                            <div class="flex items-center gap-2 text-white text-sm">
                                <i class="fas fa-medal text-yellow-400"></i>
                                <span>戍边英雄</span>
                            </div>
                        </div>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <div class="claymorphism p-6 mb-6 reveal-item" data-delay="200">
                            <h1 class="text-4xl font-bold text-[#2d3748] flex items-center gap-3">
                                <i class="fas fa-star text-[#ff4757]"></i>
                                四、榜样力量：家国担当的践行者
                            </h1>
                        </div>

                        <div class="space-y-6 text-lg text-[#2d3748] leading-relaxed">
                            <div class="glassmorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="300">
                                <div class="flex items-start gap-4">
                                    <i class="fas fa-award text-[#ff4757] mt-1.5 flex-shrink-0"></i>
                                    <div>
                                        <h2 class="text-xl font-bold text-[#2d3748] mb-2 flex items-center gap-2">
                                            英雄烈士
                                            <i class="fas fa-heart text-[#ff4757] text-sm animate-pulse"></i>
                                        </h2>
                                        <p class="text-[#636e72]">
                                            陈祥榕烈士用生命诠释<span class="animate-pulse-highlight">"清澈的爱，只为中国"</span>，体现了新时代军人的家国情怀和使命担当。
                                            <span class="audio-play-btn" data-audio="custom-audio-chenxiangrong">
                                                <i class="fas fa-volume-up"></i>
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="glassmorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="400">
                                <div class="flex items-start gap-4">
                                    <i class="fas fa-star text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                    <div>
                                        <h2 class="text-xl font-bold text-[#2d3748] mb-2 flex items-center gap-2">
                                            时代楷模
                                            <i class="fas fa-trophy text-[#ffd700] text-sm"></i>
                                        </h2>
                                        <p class="text-[#636e72]">
                                            <span class="animate-pulse-highlight">黄令仪</span>院士专注芯片研发数十年，<span class="animate-pulse-highlight">许振超</span>创造"振超效率"，展现了科技报国的赤子之心。
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="glassmorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="500">
                                <div class="flex items-start gap-4">
                                    <i class="fas fa-hands-helping text-[#2ed573] mt-1.5 flex-shrink-0"></i>
                                    <div>
                                        <h2 class="text-xl font-bold text-[#2d3748] mb-2 flex items-center gap-2">
                                            平凡英雄
                                            <i class="fas fa-users text-[#2ed573] text-sm"></i>
                                        </h2>
                                        <p class="text-[#636e72]">
                                            抗疫一线的医护人员、脱贫攻坚的驻村干部、默默奉献的基层工作者，他们用<span class="animate-pulse-highlight">平凡成就伟大</span>。
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI榜样分析 -->
                        <div class="glassmorphism-dark p-4 mt-6 reveal-item" data-delay="600">
                            <div class="flex items-center gap-3 text-white text-sm">
                                <i class="fas fa-lightbulb text-[#ffd700]"></i>
                                <span>AI分析：榜样的力量在于将抽象的家国情怀转化为具体的行动实践</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第7页：名言警句集锦 -->
        <div class="slide" id="slide7" data-reveal-count="10">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden morphing-gradient">
                <!-- AI粒子效果 -->
                <div class="ai-particles">
                    <div class="particle" style="left: 10%; animation-delay: 0s; background: rgba(255, 215, 0, 0.6);"></div>
                    <div class="particle" style="left: 30%; animation-delay: 2s; background: rgba(255, 107, 107, 0.6);"></div>
                    <div class="particle" style="left: 50%; animation-delay: 4s; background: rgba(102, 126, 234, 0.6);"></div>
                    <div class="particle" style="left: 70%; animation-delay: 6s; background: rgba(118, 75, 162, 0.6);"></div>
                    <div class="particle" style="left: 90%; animation-delay: 8s; background: rgba(240, 147, 251, 0.6);"></div>
                </div>

                <!-- 几何装饰背景 -->
                <div class="geometric-bg">
                    <div class="geometric-shape"></div>
                    <div class="geometric-shape"></div>
                    <div class="geometric-shape"></div>
                </div>

                <!-- 主背景图片 -->
                <img alt="水墨风格卷轴背景，营造典雅庄重氛围" class="opacity-20 absolute inset-0 w-full h-full z-0 object-cover object-top animate-scale-in reveal-item" src="https://space.coze.cn/s/X2E_p5psnkY/"/>
                <div class="absolute inset-0 bg-gradient-to-b from-[#667eea]/60 via-[#764ba2]/40 to-[#f093fb]/60 z-10"></div>

                <!-- 装饰元素 -->
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[900px] h-[900px] bg-radial from-[#f093fb]/20 to-transparent rounded-full pointer-events-none z-20 ai-glow"></div>
                <div class="absolute top-10 left-10 w-20 h-20 border-2 border-[#ffd700]/30 rounded-full z-20"></div>
                <div class="absolute bottom-10 right-10 w-32 h-32 border-2 border-[#ff6b6b]/20 rounded-full z-20"></div>

                <div class="relative z-30 flex flex-col items-center justify-start text-center text-white p-8 w-full h-full overflow-y-auto">
                    <header class="flex-shrink-0 mb-6 pt-4">
                        <div class="glassmorphism-dark p-6 reveal-item">
                            <h1 class="text-4xl font-bold tracking-wider text-white flex items-center justify-center gap-3">
                                <i class="fas fa-quote-left text-[#ffd700]"></i>
                                家国情怀名言警句集锦
                                <i class="fas fa-quote-right text-[#ffd700]"></i>
                            </h1>
                        </div>
                        <div class="w-32 h-1 bg-gradient-to-r from-[#ff6b6b] to-[#667eea] mt-3 mx-auto reveal-item"></div>
                    </header>

                    <!-- 名言展示区 -->
                    <main class="flex-grow w-full flex items-start justify-center min-h-0 px-4 pb-20">
                        <div class="w-full max-w-6xl grid grid-cols-2 gap-4 text-base text-white leading-relaxed">
                            <!-- 名言1 -->
                            <div class="glassmorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="100">
                                <p class="text-lg font-medium italic">
                                    "苟利国家，不求富贵"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-1">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——《礼记》</span>
                                </p>
                            </div>

                            <!-- 名言2 -->
                            <div class="glassmorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="200">
                                <p class="text-lg font-medium italic">
                                    "位卑未敢忘忧国"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-2">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——陆游</span>
                                </p>
                            </div>

                            <!-- 名言3 -->
                            <div class="glassmorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="300">
                                <p class="text-lg font-medium italic">
                                    "人生自古谁无死，留取丹心照汗青"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-3">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——文天祥</span>
                                </p>
                            </div>

                            <!-- 名言4 -->
                            <div class="glassmorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="400">
                                <p class="text-lg font-medium italic">
                                    "先天下之忧而忧，后天下之乐而乐"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-4">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——范仲淹</span>
                                </p>
                            </div>

                            <!-- 名言5 -->
                            <div class="glassmorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="500">
                                <p class="text-lg font-medium italic">
                                    "实现中华民族伟大复兴，就是中华民族近代以来最伟大的梦想"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-5">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——习近平</span>
                                </p>
                            </div>

                            <!-- 名言6 -->
                            <div class="glassmorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="600">
                                <p class="text-lg font-medium italic">
                                    "为中华之崛起而读书"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-6">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——周恩来</span>
                                </p>
                            </div>

                            <!-- 名言7 -->
                            <div class="glassmorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="700">
                                <p class="text-lg font-medium italic">
                                    "我们爱我们的民族，这是我们自信心的泉源"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-7">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——周恩来</span>
                                </p>
                            </div>

                            <!-- 名言8 -->
                            <div class="glassmorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="800">
                                <p class="text-lg font-medium italic">
                                    "路漫漫其修远兮，吾将上下而求索"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-8">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——屈原</span>
                                </p>
                            </div>
                        </div>
                    </main>

                    <!-- AI智能推荐 -->
                    <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2 glassmorphism-dark p-3 reveal-item" data-delay="900">
                        <div class="flex items-center gap-2 text-white text-sm">
                            <i class="fas fa-robot text-[#667eea] animate-pulse"></i>
                            <span>AI推荐：这些名言体现了不同时代的家国情怀精神内核</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第8页：结语 -->
        <div class="slide" id="slide8" data-reveal-count="6">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden morphing-gradient">
                <!-- AI粒子效果 -->
                <div class="ai-particles">
                    <div class="particle" style="left: 15%; animation-delay: 0s;"></div>
                    <div class="particle" style="left: 35%; animation-delay: 1s;"></div>
                    <div class="particle" style="left: 55%; animation-delay: 2s;"></div>
                    <div class="particle" style="left: 75%; animation-delay: 3s;"></div>
                </div>

                <!-- 几何装饰背景 -->
                <div class="geometric-bg">
                    <div class="geometric-shape"></div>
                    <div class="geometric-shape"></div>
                    <div class="geometric-shape"></div>
                </div>

                <!-- 主背景图片 -->
                <img alt="五星红旗飘扬在天安门广场，象征国家尊严与民族自豪" class="absolute inset-0 w-full h-full z-0 opacity-25 object-cover object-center animate-scale-in" src="https://s.coze.cn/image/mRjvTG5ZzMM/"/>

                <!-- AI光环效果 -->
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[1000px] h-[1000px] bg-radial from-[#667eea]/20 via-[#764ba2]/10 to-transparent rounded-full pointer-events-none z-10 ai-glow"></div>
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[700px] h-[700px] bg-radial from-[#f093fb]/15 to-transparent rounded-full pointer-events-none z-10 ai-glow" style="animation-delay: 2s;"></div>

                <!-- 玻璃拟态内容容器 -->
                <div class="relative z-20 flex flex-col items-center justify-center text-center p-16">
                    <!-- 主标题 -->
                    <div class="claymorphism p-8 mb-8 reveal-item">
                        <h1 class="text-5xl font-black tracking-wider text-shadow" style="background: linear-gradient(135deg, #667eea, #764ba2, #f093fb); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                            让家国情怀照亮前行之路
                        </h1>
                    </div>

                    <!-- 装饰线 -->
                    <div class="w-64 h-2 bg-gradient-to-r from-[#667eea] via-[#764ba2] to-[#f093fb] my-6 reveal-item rounded-full" data-delay="100"></div>

                    <!-- 核心内容 -->
                    <div class="glassmorphism p-8 max-w-5xl mb-8 reveal-item" data-delay="200">
                        <div class="text-xl font-medium tracking-wide text-white leading-relaxed space-y-4">
                            <p class="flex items-center justify-center gap-3">
                                <i class="fas fa-heart text-[#ff4757] animate-pulse"></i>
                                <span>家国情怀是中华民族的精神基因，是我们前行路上的明灯</span>
                                <i class="fas fa-heart text-[#ff4757] animate-pulse"></i>
                            </p>
                            <p class="flex items-center justify-center gap-3">
                                <i class="fas fa-star text-[#ffd700] animate-pulse"></i>
                                <span>让我们在新时代的征程中，传承这份深沉的爱与责任</span>
                                <i class="fas fa-star text-[#ffd700] animate-pulse"></i>
                            </p>
                            <p class="flex items-center justify-center gap-3">
                                <i class="fas fa-rocket text-[#2ed573] animate-pulse"></i>
                                <span>为实现中华民族伟大复兴的中国梦而不懈奋斗</span>
                                <i class="fas fa-rocket text-[#2ed573] animate-pulse"></i>
                            </p>
                        </div>
                    </div>

                    <!-- 感谢语 -->
                    <div class="glassmorphism-dark p-6 reveal-item" data-delay="300">
                        <div class="flex items-center justify-center gap-4 text-white">
                            <i class="fas fa-graduation-cap text-2xl text-[#667eea]"></i>
                            <div class="text-center">
                                <h3 class="text-2xl font-bold mb-2">感谢聆听</h3>
                                <p class="text-lg opacity-90">Thank you for your attention</p>
                            </div>
                            <i class="fas fa-graduation-cap text-2xl text-[#667eea]"></i>
                        </div>
                    </div>

                    <!-- AI智能总结 -->
                    <div class="claymorphism-colorful p-6 mt-8 reveal-item" data-delay="400">
                        <div class="flex items-center gap-4 text-white">
                            <i class="fas fa-robot text-2xl animate-pulse"></i>
                            <div>
                                <h3 class="text-lg font-bold mb-2 flex items-center gap-2">
                                    AI智能学习总结
                                    <i class="fas fa-sparkles text-yellow-300 text-sm"></i>
                                </h3>
                                <p class="text-base opacity-90">通过本次学习，我们深入了解了家国情怀的深刻内涵，感受了榜样的力量，让我们在实践中传承和弘扬这一宝贵精神财富。</p>
                            </div>
                        </div>
                    </div>

                    <!-- 互动提示 -->
                    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 glassmorphism-dark p-3 reveal-item" data-delay="500">
                        <div class="flex items-center gap-2 text-white text-sm">
                            <i class="fas fa-redo text-[#667eea] animate-spin"></i>
                            <span>按Home键返回首页，继续探索家国情怀的深刻内涵</span>
                        </div>
                    </div>
                </div>

                <!-- 底部渐变条 -->
                <div class="absolute bottom-0 left-0 w-full h-3 bg-gradient-to-r from-[#667eea] via-[#764ba2] via-[#f093fb] to-[#f5576c] z-20"></div>
            </div>
        </div>
    </div>

    <!-- AI智能控制栏 -->
    <div class="control-bar">
        <button class="control-btn" id="prevBtn" title="上一页"><i class="fas fa-chevron-left"></i></button>
        <button class="control-btn" id="playPauseBtn" title="播放/暂停"><i class="fas fa-play"></i></button>
        <button class="control-btn" id="nextBtn" title="下一页"><i class="fas fa-chevron-right"></i></button>
        <button class="control-btn audio-btn" id="stopAudioBtn" title="停止音频"><i class="fas fa-volume-mute"></i></button>
        <div class="progress-container" id="progressContainer" title="点击跳转到指定页面">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        <div class="slide-info" id="slideInfo">第 1 页 / 共 8 页</div>
        <button class="control-btn" id="aiAssistantBtn" title="AI助教"><i class="fas fa-robot"></i></button>
    </div>

    <script>
        // 全局变量
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        let currentSlide = 0;
        let currentRevealIndex = 0;
        let isPlaying = false;
        let playInterval;
        let audioPlayer = document.getElementById('audioPlayer');
        let currentAudioBtn = null;

        // DOM元素
        const slideInfo = document.getElementById('slideInfo');
        const progressBar = document.getElementById('progressBar');
        const progressContainer = document.getElementById('progressContainer');
        const playPauseBtn = document.getElementById('playPauseBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const stopAudioBtn = document.getElementById('stopAudioBtn');
        const aiAssistantBtn = document.getElementById('aiAssistantBtn');

        // 初始化图表
        function initCharts() {
            // 成就图表
            const achievementCtx = document.getElementById('achievementChart');
            if (achievementCtx) {
                new Chart(achievementCtx, {
                    type: 'bar',
                    data: {
                        labels: ['GDP总量', '人均收入', '教育普及率', '科技投入', '绿色发展'],
                        datasets: [
                            {
                                label: '1949年基准',
                                data: [100, 100, 100, 100, 100],
                                backgroundColor: 'rgba(255, 99, 132, 0.6)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            },
                            {
                                label: '2023年水平',
                                data: [22300, 1800, 950, 2800, 1200],
                                backgroundColor: 'rgba(54, 162, 235, 0.6)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    font: { family: "'Noto Sans SC', sans-serif", size: 12 },
                                    color: '#2d3748'
                                }
                            },
                            title: {
                                display: true,
                                text: '中国发展成就对比（以1949年为基准100）',
                                font: { family: "'Noto Sans SC', sans-serif", size: 16, weight: 'bold' },
                                color: '#2d3748',
                                padding: { bottom: 20 }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '增长倍数',
                                    font: { family: "'Noto Sans SC', sans-serif", size: 12 },
                                    color: '#2d3748'
                                },
                                ticks: {
                                    font: { family: "'Noto Sans SC', sans-serif" },
                                    color: '#2d3748'
                                }
                            },
                            x: {
                                ticks: {
                                    font: { family: "'Noto Sans SC', sans-serif", size: 11 },
                                    color: '#2d3748'
                                }
                            }
                        }
                    }
                });
            }
        }

        // 初始化
        function init() {
            updateSlideInfo();
            updateProgressBar();
            showSlide(0);
            initCharts();

            // 绑定事件
            playPauseBtn.addEventListener('click', togglePlayPause);
            prevBtn.addEventListener('click', prevSlide);
            nextBtn.addEventListener('click', nextSlide);
            stopAudioBtn.addEventListener('click', stopAllAudio);
            aiAssistantBtn.addEventListener('click', showAIAssistant);
            progressContainer.addEventListener('click', handleProgressClick);

            // 键盘事件
            document.addEventListener('keydown', handleKeyPress);

            // 音频播放按钮事件
            document.querySelectorAll('.audio-play-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    playAudio(btn.dataset.audio, btn);
                });
            });

            // 目录点击事件 - 修复逻辑
            const menuItems = document.querySelectorAll('#slide2 .glassmorphism, #slide2 .claymorphism-colorful');
            menuItems.forEach((item, index) => {
                // 为每个菜单项添加点击事件，跳转到对应页面
                item.addEventListener('click', () => {
                    const targetSlide = index + 2; // +2 因为前面有封面和目录页
                    if (targetSlide < totalSlides) {
                        showSlide(targetSlide);
                    }
                });
                item.style.cursor = 'pointer';
                item.title = `点击跳转到第${index + 2}页`;
            });
        }

        // 显示幻灯片
        function showSlide(index) {
            // 边界检查
            if (index < 0) index = 0;
            if (index >= totalSlides) index = totalSlides - 1;

            slides.forEach((slide, i) => {
                slide.classList.remove('active', 'prev', 'next');
                if (i === index) {
                    slide.classList.add('active');
                } else if (i < index) {
                    slide.classList.add('prev');
                } else {
                    slide.classList.add('next');
                }
            });

            currentSlide = index;
            currentRevealIndex = 0;
            updateSlideInfo();
            updateProgressBar();

            // 重置当前幻灯片的reveal项目
            const currentSlideElement = slides[currentSlide];
            const revealItems = currentSlideElement.querySelectorAll('.reveal-item');
            revealItems.forEach(item => {
                item.classList.remove('visible');
            });

            // 开始逐步显示
            setTimeout(() => {
                revealNextItem();
            }, 500);
        }

        // 逐步显示元素
        function revealNextItem() {
            const currentSlideElement = slides[currentSlide];
            const revealItems = currentSlideElement.querySelectorAll('.reveal-item');
            const revealCount = parseInt(currentSlideElement.dataset.revealCount) || revealItems.length;

            if (currentRevealIndex < Math.min(revealItems.length, revealCount)) {
                const item = revealItems[currentRevealIndex];
                const delay = parseInt(item.dataset.delay) || 0;

                setTimeout(() => {
                    item.classList.add('visible');
                    currentRevealIndex++;

                    // 继续显示下一个项目
                    if (currentRevealIndex < Math.min(revealItems.length, revealCount)) {
                        setTimeout(() => {
                            revealNextItem();
                        }, isPlaying ? 800 : 1200);
                    } else if (isPlaying) {
                        // 当前页面显示完毕，准备切换到下一页
                        setTimeout(() => {
                            if (currentSlide < totalSlides - 1) {
                                nextSlide();
                            } else {
                                pausePresentation();
                            }
                        }, 3000);
                    }
                }, delay);
            }
        }

        // 切换播放/暂停
        function togglePlayPause() {
            if (isPlaying) {
                pausePresentation();
            } else {
                playPresentation();
            }
        }

        // 播放演示
        function playPresentation() {
            isPlaying = true;
            playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            playPauseBtn.title = '暂停';

            // 继续显示当前幻灯片的剩余项目
            revealNextItem();
        }

        // 暂停演示
        function pausePresentation() {
            isPlaying = false;
            playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            playPauseBtn.title = '播放';
            if (playInterval) {
                clearInterval(playInterval);
            }
        }

        // 上一页
        function prevSlide() {
            if (currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // 下一页
        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            }
        }

        // 更新幻灯片信息
        function updateSlideInfo() {
            slideInfo.textContent = `第 ${currentSlide + 1} 页 / 共 ${totalSlides} 页`;
        }

        // 更新进度条
        function updateProgressBar() {
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            progressBar.style.width = `${progress}%`;
        }

        // 处理进度条点击
        function handleProgressClick(e) {
            const rect = progressContainer.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const progress = clickX / rect.width;
            const targetSlide = Math.floor(progress * totalSlides);

            if (targetSlide >= 0 && targetSlide < totalSlides) {
                showSlide(targetSlide);
            }
        }

        // 键盘事件处理
        function handleKeyPress(e) {
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    prevSlide();
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                case ' ':
                    e.preventDefault();
                    if (e.key === ' ') {
                        // 空格键：如果当前页面还有未显示的内容，继续显示；否则切换到下一页
                        const currentSlideElement = slides[currentSlide];
                        const revealItems = currentSlideElement.querySelectorAll('.reveal-item');
                        const revealCount = parseInt(currentSlideElement.dataset.revealCount) || revealItems.length;

                        if (currentRevealIndex < Math.min(revealItems.length, revealCount)) {
                            revealNextItem();
                        } else {
                            nextSlide();
                        }
                    } else {
                        nextSlide();
                    }
                    break;
                case 'Home':
                    e.preventDefault();
                    showSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    showSlide(totalSlides - 1);
                    break;
                case 'Escape':
                    e.preventDefault();
                    pausePresentation();
                    break;
                case 'F5':
                    e.preventDefault();
                    togglePlayPause();
                    break;
            }
        }

        // 播放音频
        function playAudio(audioSrc, btn) {
            if (currentAudioBtn && currentAudioBtn !== btn) {
                currentAudioBtn.classList.remove('playing');
            }

            if (audioSrc && !audioSrc.startsWith('custom-audio')) {
                audioPlayer.src = audioSrc;
                audioPlayer.play().then(() => {
                    btn.classList.add('playing');
                    currentAudioBtn = btn;
                }).catch(e => {
                    console.log('音频播放失败:', e);
                    simulateAudioPlay(btn);
                });
            } else {
                simulateAudioPlay(btn);
            }
        }

        // 模拟音频播放
        function simulateAudioPlay(btn) {
            btn.classList.add('playing');
            currentAudioBtn = btn;
            setTimeout(() => {
                btn.classList.remove('playing');
                if (currentAudioBtn === btn) {
                    currentAudioBtn = null;
                }
            }, 3000);
        }

        // 停止所有音频
        function stopAllAudio() {
            audioPlayer.pause();
            audioPlayer.currentTime = 0;
            if (currentAudioBtn) {
                currentAudioBtn.classList.remove('playing');
                currentAudioBtn = null;
            }
        }

        // AI助教功能
        function showAIAssistant() {
            const messages = [
                "🤖 AI助教提示：当前正在学习家国情怀主题，建议结合历史背景深入理解。",
                "💡 学习建议：可以通过键盘方向键或空格键控制播放进度。",
                "📚 知识拓展：家国情怀是中华优秀传统文化的重要组成部分。",
                "🎯 重点关注：注意理解不同历史时期家国情怀的具体表现。",
                "🌟 互动提示：点击音频按钮可以听取相关内容的语音解读。"
            ];

            const randomMessage = messages[Math.floor(Math.random() * messages.length)];

            // 创建提示框
            const tooltip = document.createElement('div');
            tooltip.className = 'fixed bottom-20 left-1/2 transform -translate-x-1/2 glassmorphism-dark p-4 text-white text-sm rounded-lg z-50 max-w-md text-center';
            tooltip.innerHTML = `
                <div class="flex items-center gap-2 mb-2">
                    <i class="fas fa-robot text-[#667eea]"></i>
                    <span class="font-bold">AI智能助教</span>
                </div>
                <p>${randomMessage}</p>
            `;

            document.body.appendChild(tooltip);

            // 3秒后自动消失
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 4000);
        }

        // 音频结束事件
        audioPlayer.addEventListener('ended', () => {
            if (currentAudioBtn) {
                currentAudioBtn.classList.remove('playing');
                currentAudioBtn = null;
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);

        // 页面可见性变化时暂停播放
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && isPlaying) {
                pausePresentation();
            }
        });
    </script>
</body>
</html>
