<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智华AI - 培育家国情怀的智慧教育平台</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary: #ff6b6b;
            --secondary: #4dabf7;
            --accent: #ffd43b;
            --gradient-1: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-2: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-3: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --gradient-4: linear-gradient(135deg, #30cfd0 0%, #330867 100%);
            --soft-shadow: 20px 20px 60px #d1d1d1, -20px -20px 60px #ffffff;
            --soft-shadow-hover: 30px 30px 80px #c8c8c8, -30px -30px 80px #ffffff;
            --text-dark: #2d3436;
            --bg-light: #f5f7fa;
            --card-bg: rgba(255, 255, 255, 0.95);
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #e6f3ff 50%, #fff9e6 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }
        
        /* 几何背景装饰 */
        .geometric-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.6;
        }
        
        .floating-shape {
            position: absolute;
            border-radius: 50%;
            filter: blur(40px);
            animation: float 20s infinite ease-in-out;
        }
        
        .shape-1 {
            width: 400px;
            height: 400px;
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            top: -200px;
            right: -100px;
            animation-delay: 0s;
        }
        
        .shape-2 {
            width: 300px;
            height: 300px;
            background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
            bottom: -150px;
            left: -100px;
            animation-delay: 5s;
        }
        
        .shape-3 {
            width: 250px;
            height: 250px;
            background: linear-gradient(135deg, #ff9ff3 0%, #ee5a24 100%);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: 10s;
        }
        
        @keyframes float {
            0%, 100% { transform: translate(0, 0) rotate(0deg) scale(1); }
            33% { transform: translate(30px, -30px) rotate(120deg) scale(1.1); }
            66% { transform: translate(-20px, 20px) rotate(240deg) scale(0.9); }
        }
        
        /* 导航栏 */
        nav {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ff6b6b 0%, #4dabf7 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffd43b 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.1), -5px -5px 15px rgba(255, 255, 255, 0.7);
            position: relative;
            overflow: hidden;
        }
        
        .logo-icon::before {
            content: "智";
            color: white;
            font-size: 24px;
            font-weight: 900;
            z-index: 1;
            position: relative;
        }
        
        .logo-icon::after {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .nav-menu {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-menu a {
            text-decoration: none;
            color: var(--text-dark);
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .nav-menu a::after {
            content: "";
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #4dabf7);
            transition: width 0.3s ease;
        }
        
        .nav-menu a:hover::after {
            width: 100%;
        }
        
        /* 主英雄区域 */
        .hero {
            padding: 4rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            min-height: 80vh;
        }
        
        .hero-content h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 900;
            line-height: 1.2;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ff6b6b 0%, #4dabf7 50%, #ffd43b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradient-shift 5s ease infinite;
        }
        
        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .hero-content p {
            font-size: 1.25rem;
            color: #636e72;
            margin-bottom: 2rem;
            line-height: 1.8;
        }
        
        .hero-buttons {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;
        }
        
        .btn-primary, .btn-secondary {
            padding: 1rem 2.5rem;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8787 100%);
            color: white;
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            color: #ff6b6b;
            border: 2px solid rgba(255, 107, 107, 0.3);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .btn-secondary:hover {
            transform: translateY(-3px);
            background: rgba(255, 107, 107, 0.1);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        /* 3D卡片效果 */
        .hero-visual {
            position: relative;
            perspective: 1000px;
        }
        
        .hero-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 30px;
            padding: 3rem;
            box-shadow: var(--soft-shadow);
            transform-style: preserve-3d;
            transition: all 0.6s ease;
            position: relative;
            overflow: hidden;
        }
        
        .hero-card:hover {
            transform: rotateY(5deg) rotateX(-5deg);
            box-shadow: var(--soft-shadow-hover);
        }
        
        .hero-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(77, 171, 247, 0.1) 100%);
            border-radius: 30px;
            z-index: -1;
        }
        
        .china-map-illustration {
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffd43b 100%);
            border-radius: 20px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .china-map-illustration::before {
            content: "🇨🇳";
            font-size: 150px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.1); opacity: 1; }
        }
        
        /* 特色功能区 */
        .features {
            padding: 4rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }
        
        .section-header h2 {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #ff6b6b 0%, #4dabf7 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .section-header p {
            font-size: 1.25rem;
            color: #636e72;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2.5rem;
            position: relative;
            overflow: hidden;
            box-shadow: 10px 10px 30px rgba(0, 0, 0, 0.05), -10px -10px 30px rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 15px 15px 40px rgba(0, 0, 0, 0.08), -15px -15px 40px rgba(255, 255, 255, 0.9);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 1.5rem;
            position: relative;
            box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.05), inset -5px -5px 10px rgba(255, 255, 255, 0.8);
        }
        
        .icon-gradient-1 { background: linear-gradient(135deg, #ff6b6b 0%, #ff8787 100%); }
        .icon-gradient-2 { background: linear-gradient(135deg, #4dabf7 0%, #74c0fc 100%); }
        .icon-gradient-3 { background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%); }
        .icon-gradient-4 { background: linear-gradient(135deg, #51cf66 0%, #8ce99a 100%); }
        
        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }
        
        .feature-card p {
            color: #636e72;
            line-height: 1.6;
        }
        
        /* 家国情怀板块 */
        .patriotic-section {
            padding: 4rem 2rem;
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, rgba(77, 171, 247, 0.05) 100%);
            margin: 4rem 0;
            position: relative;
            overflow: hidden;
        }
        
        .patriotic-content {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .patriotic-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .patriotic-card::before {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 107, 107, 0.05) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .patriotic-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.08);
        }
        
        .patriotic-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: inline-block;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        .patriotic-card h4 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffd43b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* AI互动模块 */
        .ai-interactive {
            padding: 4rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .ai-chat-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 30px;
            padding: 3rem;
            box-shadow: var(--soft-shadow);
            position: relative;
            overflow: hidden;
        }
        
        .ai-chat-header {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 2px solid rgba(0, 0, 0, 0.05);
        }
        
        .ai-avatar {
            width: 80px;
            height: 80px;
            border-radius: 25px;
            background: linear-gradient(135deg, #4dabf7 0%, #667eea 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: 0 10px 25px rgba(77, 171, 247, 0.3);
            animation: float-avatar 3s ease-in-out infinite;
        }
        
        @keyframes float-avatar {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        .ai-status {
            flex: 1;
        }
        
        .ai-status h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .ai-status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: #51cf66;
            font-weight: 500;
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            background: #51cf66;
            border-radius: 50%;
            animation: blink 2s infinite;
        }
        
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
        
        .chat-suggestions {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .suggestion-chip {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(77, 171, 247, 0.1) 100%);
            border-radius: 50px;
            border: 1px solid rgba(77, 171, 247, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .suggestion-chip:hover {
            transform: translateY(-2px);
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.2) 0%, rgba(77, 171, 247, 0.2) 100%);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        /* 数据可视化 */
        .stats-section {
            padding: 4rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 10px 10px 30px rgba(0, 0, 0, 0.05), -10px -10px 30px rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: scale(1.05);
            box-shadow: 15px 15px 40px rgba(0, 0, 0, 0.08), -15px -15px 40px rgba(255, 255, 255, 0.9);
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            background: linear-gradient(135deg, #ff6b6b 0%, #4dabf7 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #636e72;
            font-weight: 500;
        }
        
        /* 页脚 */
        footer {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, rgba(77, 171, 247, 0.05) 100%);
            padding: 3rem 2rem;
            margin-top: 4rem;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .footer-content {
            max-width: 1400px;
            margin: 0 auto;
            text-align: center;
        }
        
        .footer-logo {
            font-size: 2rem;
            font-weight: 900;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #ff6b6b 0%, #4dabf7 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        
        .footer-links a {
            color: #636e72;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: #ff6b6b;
        }
        
        .social-icons {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .social-icon {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.05), -5px -5px 15px rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .social-icon:hover {
            transform: translateY(-5px);
            box-shadow: 8px 8px 20px rgba(0, 0, 0, 0.08), -8px -8px 20px rgba(255, 255, 255, 0.9);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .nav-menu {
                display: none;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 几何背景装饰 -->
    <div class="geometric-bg">
        <div class="floating-shape shape-1"></div>
        <div class="floating-shape shape-2"></div>
        <div class="floating-shape shape-3"></div>
    </div>
    
    <!-- 导航栏 -->
    <nav>
        <div class="nav-container">
            <div class="logo">
                <div class="logo-icon"></div>
                <span>智华AI教育</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home">首页</a></li>
                <li><a href="#features">核心功能</a></li>
                <li><a href="#patriotic">家国情怀</a></li>
                <li><a href="#ai">AI助教</a></li>
                <li><a href="#about">关于我们</a></li>
            </ul>
        </div>
    </nav>
    
    <!-- 主英雄区域 -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>AI赋能教育<br>培育家国情怀</h1>
            <p>融合人工智能技术与传统文化教育，打造新时代智慧学习平台。让每一个学子在科技创新中传承中华文明，在智能教育中厚植爱国情怀。</p>
            <div class="hero-buttons">
                <button class="btn-primary">开启智慧之旅</button>
                <button class="btn-secondary">了解更多</button>
            </div>
        </div>
        <div class="hero-visual">
            <div class="hero-card">
                <div class="china-map-illustration"></div>
            </div>
        </div>
    </section>
    
    <!-- 特色功能区 -->
    <section class="features" id="features">
        <div class="section-header">
            <h2>核心功能模块</h2>
            <p>AI技术驱动，全方位提升学习体验</p>
        </div>
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon icon-gradient-1">📚</div>
                <h3>智能课程定制</h3>
                <p>基于深度学习算法，为每位学生量身定制个性化学习路径，融入传统文化与现代科技知识。</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon icon-gradient-2">🤖</div>
                <h3>AI助教答疑</h3>
                <p>24小时智能助教在线，实时解答学习疑问，提供多维度知识拓展，培养批判性思维。</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon icon-gradient-3">📊</div>
                <h3>学情智能分析</h3>
                <p>多维度数据分析，精准把握学习进度，生成可视化报告，助力教育决策科学化。</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon icon-gradient-4">🎯</div>
                <h3>素质全面评价</h3>
                <p>构建德智体美劳全面发展评价体系，注重品德培养与家国情怀教育。</p>
            </div>
        </div>
    </section>
    
    <!-- 家国情怀板块 -->
    <section class="patriotic-section" id="patriotic">
        <div class="section-header">
            <h2>厚植家国情怀</h2>
            <p>传承中华文明，培育时代新人</p>
        </div>
        <div class="patriotic-content">
            <div class="patriotic-card">
                <div class="patriotic-icon">🏛️</div>
                <h4>文化传承</h4>