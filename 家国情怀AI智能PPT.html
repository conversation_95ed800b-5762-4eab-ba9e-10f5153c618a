<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>家国情怀：从历史传承到时代担当 - AI智能交互式PPT</title>
    <script src="https://unpkg.byted-static.com/coze/space_ppt_lib/1.0.3-alpha.12/lib/tailwindcss.js"></script>
    <script src="https://unpkg.byted-static.com/chart.js/4.5.0/dist/chart.umd.js"></script>
    <script src="https://unpkg.byted-static.com/fortawesome/fontawesome-free/6.7.2/js/all.min.js" data-auto-replace-svg="nest"></script>
    <link href="https://lf-code-agent.coze.cn/obj/x-ai-cn/fonts/google/google-all-fonts.css" rel="stylesheet"/>
    <style>
        .font-noto { font-family: 'Noto Sans SC', sans-serif; }
        body { 
            margin: 0; 
            padding: 0; 
            overflow: hidden; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        /* 新拟物化基础样式 */
        .neumorphism {
            background: #f0f0f3;
            border-radius: 20px;
            box-shadow: 
                20px 20px 60px #bebebe,
                -20px -20px 60px #ffffff;
        }
        
        .neumorphism-inset {
            background: #f0f0f3;
            border-radius: 20px;
            box-shadow: 
                inset 20px 20px 60px #bebebe,
                inset -20px -20px 60px #ffffff;
        }
        
        .neumorphism-dark {
            background: #2d3748;
            border-radius: 20px;
            box-shadow: 
                20px 20px 60px #1a202c,
                -20px -20px 60px #404a5c;
        }
        
        /* 几何装饰元素 */
        .geometric-bg {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .geometric-shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        .geometric-shape:nth-child(1) {
            top: 10%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 50%;
            animation-delay: 0s;
        }
        
        .geometric-shape:nth-child(2) {
            top: 20%;
            right: 15%;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #48dbfb, #0abde3);
            transform: rotate(45deg);
            animation-delay: 2s;
        }
        
        .geometric-shape:nth-child(3) {
            bottom: 20%;
            left: 20%;
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #ff9ff3, #f368e0);
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        /* PPT容器样式 */
        .ppt-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 单页幻灯片样式 */
        .slide {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transition: all 1.2s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            transform: scale(0.95) translateY(30px);
            filter: blur(3px);
        }
        .slide.active {
            opacity: 1;
            z-index: 10;
            transform: scale(1) translateY(0);
            filter: blur(0);
        }
        .slide.next {
            transform: translateX(100%) scale(0.9);
            filter: blur(5px);
        }
        .slide.prev {
            transform: translateX(-100%) scale(0.9);
            filter: blur(5px);
        }
        
        /* AI智能控制栏样式 */
        .control-bar {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 15px 25px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 30px;
            max-width: 900px;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .control-bar:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateX(-50%) translateY(-3px);
            box-shadow: 
                0 15px 45px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
        
        /* 新拟物化按钮 */
        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(145deg, #f0f0f3, #cacaca);
            color: #667eea;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 
                8px 8px 16px #bebebe,
                -8px -8px 16px #ffffff;
            position: relative;
            overflow: hidden;
        }
        
        .control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(145deg, transparent, rgba(255, 255, 255, 0.1));
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 
                12px 12px 24px #bebebe,
                -12px -12px 24px #ffffff;
            color: #5a67d8;
        }
        
        .control-btn:hover::before {
            opacity: 1;
        }
        
        .control-btn:active {
            transform: translateY(0);
            box-shadow: 
                inset 8px 8px 16px #bebebe,
                inset -8px -8px 16px #ffffff;
        }
        
        .control-btn.audio-btn {
            background: linear-gradient(145deg, #ff6b6b, #ee5a52);
            color: white;
            box-shadow: 
                8px 8px 16px #d63031,
                -8px -8px 16px #ff7675;
        }
        
        .control-btn.audio-btn:hover {
            background: linear-gradient(145deg, #ee5a52, #d63031);
            box-shadow: 
                12px 12px 24px #d63031,
                -12px -12px 24px #ff7675;
        }
        
        /* 进度条新拟物化设计 */
        .progress-container {
            flex: 1;
            height: 8px;
            background: #f0f0f3;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            box-shadow: 
                inset 4px 4px 8px #bebebe,
                inset -4px -4px 8px #ffffff;
            position: relative;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
            position: relative;
        }
        
        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
            border-radius: 0 10px 10px 0;
        }
        
        .slide-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-family: 'Noto Sans SC', sans-serif;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        /* AI智能逐点显示元素样式 */
        .reveal-item {
            opacity: 0;
            transform: translateY(40px) scale(0.9);
            transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
            filter: blur(2px);
            position: relative;
        }
        
        .reveal-item.visible {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0);
        }
        
        .reveal-item::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
            border-radius: 10px;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
            filter: blur(10px);
        }
        
        .reveal-item:hover::before {
            opacity: 0.3;
        }

        /* AI音频播放按钮新拟物化设计 */
        .audio-play-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(145deg, #ff6b6b, #ee5a52);
            color: white;
            cursor: pointer;
            margin-left: 10px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            vertical-align: middle;
            box-shadow:
                6px 6px 12px rgba(238, 90, 82, 0.3),
                -6px -6px 12px rgba(255, 107, 107, 0.3);
            position: relative;
            overflow: hidden;
        }

        .audio-play-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(145deg, transparent, rgba(255, 255, 255, 0.2));
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .audio-play-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow:
                8px 8px 16px rgba(238, 90, 82, 0.4),
                -8px -8px 16px rgba(255, 107, 107, 0.4);
        }

        .audio-play-btn:hover::before {
            opacity: 1;
        }

        .audio-play-btn.playing {
            animation: aiPulse 2s infinite;
        }

        @keyframes aiPulse {
            0% {
                transform: scale(1);
                box-shadow:
                    6px 6px 12px rgba(238, 90, 82, 0.3),
                    -6px -6px 12px rgba(255, 107, 107, 0.3),
                    0 0 0 0 rgba(255, 107, 107, 0.7);
            }
            50% {
                transform: scale(1.1);
                box-shadow:
                    8px 8px 16px rgba(238, 90, 82, 0.4),
                    -8px -8px 16px rgba(255, 107, 107, 0.4),
                    0 0 0 15px rgba(255, 107, 107, 0);
            }
            100% {
                transform: scale(1);
                box-shadow:
                    6px 6px 12px rgba(238, 90, 82, 0.3),
                    -6px -6px 12px rgba(255, 107, 107, 0.3),
                    0 0 0 0 rgba(255, 107, 107, 0);
            }
        }

        /* AI智能动画样式 */
        @keyframes aiGlow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 40px rgba(102, 126, 234, 0.6);
                transform: scale(1.02);
            }
        }

        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(30px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            0% { transform: translateY(60px) scale(0.95); opacity: 0; filter: blur(3px); }
            100% { transform: translateY(0) scale(1); opacity: 1; filter: blur(0); }
        }

        @keyframes scaleIn {
            0% { transform: scale(0.8) rotate(-5deg); opacity: 0; filter: blur(5px); }
            100% { transform: scale(1) rotate(0deg); opacity: 1; filter: blur(0); }
        }

        @keyframes pulseHighlight {
            0% {
                background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.2));
                transform: scale(1);
            }
            50% {
                background: linear-gradient(45deg, rgba(255, 215, 0, 0.4), rgba(255, 193, 7, 0.4));
                transform: scale(1.05);
            }
            100% {
                background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.2));
                transform: scale(1);
            }
        }

        @keyframes underlinePulse {
            0% {
                text-decoration-thickness: 2px;
                opacity: 1;
                text-shadow: 0 0 10px rgba(255, 71, 87, 0.3);
            }
            50% {
                text-decoration-thickness: 4px;
                opacity: 0.8;
                text-shadow: 0 0 20px rgba(255, 71, 87, 0.6);
            }
            100% {
                text-decoration-thickness: 2px;
                opacity: 1;
                text-shadow: 0 0 10px rgba(255, 71, 87, 0.3);
            }
        }

        @keyframes morphingGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .animate-fade-in { animation: fadeIn 1s cubic-bezier(0.4, 0, 0.2, 1) forwards; }
        .animate-slide-up { animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1) forwards; }
        .animate-scale-in { animation: scaleIn 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards; }
        .animate-pulse-highlight {
            animation: pulseHighlight 3s infinite;
            padding: 2px 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .animate-underline-pulse {
            animation: underlinePulse 2s infinite;
            text-decoration: underline;
            text-decoration-color: #ff4757;
            position: relative;
        }

        .ai-glow { animation: aiGlow 4s ease-in-out infinite; }

        .text-shadow {
            text-shadow:
                0 2px 10px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(102, 126, 234, 0.2);
        }

        .bg-radial { background-image: radial-gradient(circle, var(--tw-gradient-stops)); }

        .morphing-gradient {
            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 400% 400%;
            animation: morphingGradient 8s ease infinite;
        }
    </style>
</head>
<body class="font-noto">
    <!-- 音频元素（隐藏） -->
    <audio id="audioPlayer" preload="none">
        <source src="" type="audio/mpeg">
    </audio>

    <!-- PPT容器 -->
    <div class="ppt-container" id="pptContainer">
        <!-- 第1页：AI智能封面 -->
        <div class="slide active" id="slide1" data-reveal-count="4">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden morphing-gradient">
                <!-- 几何装饰背景 -->
                <div class="geometric-bg">
                    <div class="geometric-shape"></div>
                    <div class="geometric-shape"></div>
                    <div class="geometric-shape"></div>
                </div>

                <!-- 主背景图片 -->
                <img alt="蜿蜒于山峦间的长城剪影，展现历史厚重感" class="absolute inset-0 w-full h-full z-0 opacity-30 object-cover object-top animate-scale-in" src="https://s.coze.cn/image/mRjvTG5ZzMM/"/>

                <!-- AI光环效果 -->
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-radial from-[#667eea]/20 via-[#764ba2]/10 to-transparent rounded-full pointer-events-none z-10 ai-glow"></div>
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-radial from-[#f093fb]/15 to-transparent rounded-full pointer-events-none z-10 ai-glow" style="animation-delay: 2s;"></div>

                <!-- 新拟物化内容容器 -->
                <div class="relative z-20 flex flex-col items-center justify-center text-center p-12">
                    <!-- AI智能标识 -->
                    <div class="neumorphism-dark px-6 py-3 mb-8 reveal-item">
                        <div class="flex items-center gap-3 text-white">
                            <i class="fas fa-brain text-[#667eea] text-xl"></i>
                            <span class="text-sm font-medium tracking-wider">AI智能教育系统</span>
                            <div class="w-2 h-2 bg-[#2ed573] rounded-full animate-pulse"></div>
                        </div>
                    </div>

                    <!-- 主标题 -->
                    <div class="neumorphism p-8 mb-8 reveal-item" data-delay="100">
                        <h1 class="text-5xl font-black tracking-wider text-shadow" style="background: linear-gradient(135deg, #667eea, #764ba2, #f093fb); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                            家国情怀：从历史传承到时代担当
                        </h1>
                    </div>

                    <!-- 装饰线 -->
                    <div class="w-48 h-2 bg-gradient-to-r from-[#667eea] via-[#764ba2] to-[#f093fb] my-6 reveal-item rounded-full" data-delay="200"></div>

                    <!-- 副标题 -->
                    <div class="neumorphism-inset p-6 max-w-4xl reveal-item" data-delay="300">
                        <p class="text-xl font-medium tracking-wide text-[#2d3748]">
                            <i class="fas fa-graduation-cap text-[#667eea] mr-3"></i>
                            爱国主义精神、民族自豪感与社会责任感的当代诠释
                            <i class="fas fa-heart text-[#f093fb] ml-3"></i>
                        </p>
                    </div>
                </div>

                <!-- 底部渐变条 -->
                <div class="absolute bottom-0 left-0 w-full h-3 bg-gradient-to-r from-[#667eea] via-[#764ba2] via-[#f093fb] to-[#f5576c] z-20"></div>
            </div>
        </div>

        <!-- 第2页：AI智能目录 -->
        <div class="slide" id="slide2" data-reveal-count="10">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f0f0f3] p-16">
                <!-- 几何装饰背景 -->
                <div class="geometric-bg">
                    <div class="geometric-shape" style="top: 15%; right: 10%; width: 120px; height: 120px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 20px;"></div>
                    <div class="geometric-shape" style="bottom: 15%; left: 15%; width: 80px; height: 80px; background: linear-gradient(45deg, #f093fb, #f5576c); clip-path: polygon(50% 0%, 0% 100%, 100% 100%);"></div>
                </div>

                <!-- AI智能网格背景 -->
                <div class="absolute inset-0 opacity-5 z-0">
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                                <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#667eea" stroke-width="1"/>
                            </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#grid)" />
                    </svg>
                </div>

                <!-- 标题区域 -->
                <header class="flex-shrink-0 w-full mb-12 z-10">
                    <div class="neumorphism p-6 inline-block reveal-item">
                        <div class="flex items-center gap-4">
                            <i class="fas fa-list-ul text-3xl text-[#667eea]"></i>
                            <h1 class="text-5xl font-bold text-[#2d3748]">智能目录导航</h1>
                            <div class="w-3 h-3 bg-[#2ed573] rounded-full animate-pulse"></div>
                        </div>
                    </div>
                    <div class="w-32 h-2 bg-gradient-to-r from-[#667eea] to-[#764ba2] mt-4 rounded-full reveal-item"></div>
                </header>

                <!-- 目录内容 -->
                <main class="flex-grow w-full flex items-center min-h-0 z-10">
                    <div class="w-full grid grid-cols-2 gap-8">
                        <!-- 左列 -->
                        <div class="space-y-4">
                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="100">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center text-white font-bold">1</div>
                                    <span class="text-xl font-medium text-[#2d3748]">家国情怀的内涵解析</span>
                                    <i class="fas fa-chevron-right text-[#667eea] ml-auto"></i>
                                </div>
                            </div>

                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="200">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#764ba2] to-[#f093fb] rounded-full flex items-center justify-center text-white font-bold">2</div>
                                    <span class="text-xl font-medium text-[#2d3748]">历史长河中的家国记忆</span>
                                    <i class="fas fa-chevron-right text-[#764ba2] ml-auto"></i>
                                </div>
                            </div>

                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="300">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f093fb] to-[#f5576c] rounded-full flex items-center justify-center text-white font-bold">3</div>
                                    <span class="text-xl font-medium text-[#2d3748]">民族复兴的辉煌成就</span>
                                    <i class="fas fa-chevron-right text-[#f093fb] ml-auto"></i>
                                </div>
                            </div>

                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300 animate-underline-pulse" data-delay="400">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f5576c] to-[#667eea] rounded-full flex items-center justify-center text-white font-bold">4</div>
                                    <span class="text-xl font-medium text-[#2d3748]">榜样力量：家国担当的践行者</span>
                                    <i class="fas fa-star text-[#f5576c] ml-auto animate-pulse"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 右列 -->
                        <div class="space-y-4">
                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="500">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center text-white font-bold">5</div>
                                    <span class="text-xl font-medium text-[#2d3748]">家国情怀的教育实践</span>
                                    <i class="fas fa-chevron-right text-[#667eea] ml-auto"></i>
                                </div>
                            </div>

                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="600">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#764ba2] to-[#f093fb] rounded-full flex items-center justify-center text-white font-bold">6</div>
                                    <span class="text-xl font-medium text-[#2d3748]">传统节日中的家国情怀</span>
                                    <i class="fas fa-chevron-right text-[#764ba2] ml-auto"></i>
                                </div>
                            </div>

                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="700">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f093fb] to-[#f5576c] rounded-full flex items-center justify-center text-white font-bold">7</div>
                                    <span class="text-xl font-medium text-[#2d3748]">科技领域的家国情怀</span>
                                    <i class="fas fa-chevron-right text-[#f093fb] ml-auto"></i>
                                </div>
                            </div>

                            <div class="neumorphism p-4 reveal-item hover:scale-105 transition-transform duration-300" data-delay="800">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-[#f5576c] to-[#667eea] rounded-full flex items-center justify-center text-white font-bold">8</div>
                                    <span class="text-xl font-medium text-[#2d3748]">结语：让家国情怀照亮前行之路</span>
                                    <i class="fas fa-chevron-right text-[#f5576c] ml-auto"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

                <!-- 底部进度指示器 -->
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-10 reveal-item" data-delay="900">
                    <div class="w-3 h-3 bg-[#667eea] rounded-full animate-pulse"></div>
                    <div class="w-3 h-3 bg-[#764ba2] rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                    <div class="w-3 h-3 bg-[#f093fb] rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                    <div class="w-3 h-3 bg-[#f5576c] rounded-full animate-pulse" style="animation-delay: 0.6s;"></div>
                </div>
            </div>
        </div>

        <!-- 第3页：AI智能内涵解析 -->
        <div class="slide" id="slide3" data-reveal-count="6">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f0f0f3] p-16">
                <!-- 几何装饰背景 -->
                <div class="geometric-bg">
                    <div class="geometric-shape" style="bottom: 10%; right: 10%; width: 150px; height: 150px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 50%; opacity: 0.05;"></div>
                </div>

                <!-- AI智能图标装饰 -->
                <div class="absolute bottom-8 right-8 text-[120px] font-black text-[#667eea]/8 select-none pointer-events-none -rotate-12 z-0 animate-scale-in">
                    <i class="fas fa-brain"></i>
                </div>

                <!-- 标题区域 -->
                <header class="flex-shrink-0 w-full mb-10 z-10">
                    <div class="neumorphism p-6 inline-block reveal-item">
                        <div class="flex items-center gap-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center text-white">
                                <i class="fas fa-lightbulb text-xl"></i>
                            </div>
                            <h1 class="text-4xl font-bold text-[#2d3748]">一、家国情怀的内涵解析</h1>
                        </div>
                    </div>
                    <div class="w-32 h-2 bg-gradient-to-r from-[#667eea] to-[#764ba2] mt-4 rounded-full reveal-item"></div>
                </header>

                <!-- 内容区域 -->
                <main class="flex-grow w-full flex flex-col justify-center min-h-0 z-10">
                    <div class="space-y-6">
                        <!-- 核心定义 -->
                        <div class="neumorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="100">
                            <div class="flex items-start gap-6">
                                <div class="neumorphism-inset w-16 h-16 flex items-center justify-center text-[#667eea] text-2xl rounded-full flex-shrink-0">
                                    <i class="fas fa-book-open"></i>
                                </div>
                                <div class="text-xl text-[#2d3748] leading-relaxed">
                                    <strong class="font-bold text-[#667eea] text-2xl">核心定义：</strong>
                                    <br>
                                    <span class="text-lg">个人对家庭和国家共同体的认同与热爱，是爱国主义精神的伦理基础和情感状态。</span>
                                </div>
                            </div>
                        </div>

                        <!-- 历史渊源 -->
                        <div class="neumorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="200">
                            <div class="flex items-start gap-6">
                                <div class="neumorphism-inset w-16 h-16 flex items-center justify-center text-[#764ba2] text-2xl rounded-full flex-shrink-0">
                                    <i class="fas fa-history"></i>
                                </div>
                                <div class="text-xl text-[#2d3748] leading-relaxed">
                                    <strong class="font-bold text-[#764ba2] text-2xl">历史渊源：</strong>
                                    <br>
                                    <span class="text-lg">根植于中国氏族血缘宗法制，《礼记》<span class="animate-pulse-highlight">"五止十义"</span>奠定伦理与政治秩序统一的基础。</span>
                                </div>
                            </div>
                        </div>

                        <!-- 现代升华 -->
                        <div class="neumorphism p-6 reveal-item hover:scale-105 transition-all duration-300" data-delay="300">
                            <div class="flex items-start gap-6">
                                <div class="neumorphism-inset w-16 h-16 flex items-center justify-center text-[#f093fb] text-2xl rounded-full flex-shrink-0">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <div class="text-xl text-[#2d3748] leading-relaxed">
                                    <strong class="font-bold text-[#f093fb] text-2xl">现代升华：</strong>
                                    <br>
                                    <span class="text-lg">习近平总书记指出<span class="animate-pulse-highlight">"爱国是本分，也是职责"</span></span>
                                    <span class="audio-play-btn" data-audio="custom-audio-xi">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- AI智能总结 -->
                        <div class="neumorphism-dark p-6 reveal-item" data-delay="400">
                            <div class="flex items-center gap-4 text-white">
                                <i class="fas fa-robot text-2xl text-[#667eea]"></i>
                                <div>
                                    <h3 class="text-lg font-bold mb-2">AI智能总结</h3>
                                    <p class="text-base opacity-90">家国情怀体现了个人与集体、传统与现代、情感与理性的有机统一，是中华民族精神的重要组成部分。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- 第4页：名言警句集锦 -->
        <div class="slide" id="slide4" data-reveal-count="9">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden morphing-gradient">
                <!-- 几何装饰背景 -->
                <div class="geometric-bg">
                    <div class="geometric-shape"></div>
                    <div class="geometric-shape"></div>
                    <div class="geometric-shape"></div>
                </div>

                <!-- 主背景图片 -->
                <img alt="水墨风格卷轴背景，营造典雅庄重氛围" class="opacity-20 absolute inset-0 w-full h-full z-0 object-cover object-top animate-scale-in reveal-item" src="https://space.coze.cn/s/X2E_p5psnkY/"/>
                <div class="absolute inset-0 bg-gradient-to-b from-[#667eea]/60 via-[#764ba2]/40 to-[#f093fb]/60 z-10"></div>

                <!-- 装饰元素 -->
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[900px] h-[900px] bg-radial from-[#f093fb]/20 to-transparent rounded-full pointer-events-none z-20 ai-glow"></div>
                <div class="absolute top-10 left-10 w-20 h-20 border-2 border-[#ffd700]/30 rounded-full z-20"></div>
                <div class="absolute bottom-10 right-10 w-32 h-32 border-2 border-[#ff6b6b]/20 rounded-full z-20"></div>

                <div class="relative z-30 flex flex-col items-center justify-start text-center text-white p-8 w-full h-full overflow-y-auto">
                    <header class="flex-shrink-0 mb-6 pt-4">
                        <div class="neumorphism-dark p-6 reveal-item">
                            <h1 class="text-4xl font-bold tracking-wider text-white">
                                <i class="fas fa-quote-left text-[#ffd700] mr-3"></i>
                                家国情怀名言警句集锦
                                <i class="fas fa-quote-right text-[#ffd700] ml-3"></i>
                            </h1>
                        </div>
                        <div class="w-32 h-1 bg-gradient-to-r from-[#ff6b6b] to-[#667eea] mt-3 mx-auto reveal-item"></div>
                    </header>

                    <!-- 名言展示区 -->
                    <main class="flex-grow w-full flex items-start justify-center min-h-0 px-4 pb-20">
                        <div class="w-full max-w-6xl grid grid-cols-2 gap-4 text-base text-white leading-relaxed">
                            <!-- 名言1 -->
                            <div class="neumorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="100">
                                <p class="text-lg font-medium italic">
                                    "苟利国家，不求富贵"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-1">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——《礼记》</span>
                                </p>
                            </div>

                            <!-- 名言2 -->
                            <div class="neumorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="200">
                                <p class="text-lg font-medium italic">
                                    "位卑未敢忘忧国"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-2">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——陆游</span>
                                </p>
                            </div>

                            <!-- 名言3 -->
                            <div class="neumorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="300">
                                <p class="text-lg font-medium italic">
                                    "人生自古谁无死，留取丹心照汗青"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-3">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——文天祥</span>
                                </p>
                            </div>

                            <!-- 名言4 -->
                            <div class="neumorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="400">
                                <p class="text-lg font-medium italic">
                                    "先天下之忧而忧，后天下之乐而乐"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-4">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——范仲淹</span>
                                </p>
                            </div>

                            <!-- 名言5 -->
                            <div class="neumorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="500">
                                <p class="text-lg font-medium italic">
                                    "实现中华民族伟大复兴，就是中华民族近代以来最伟大的梦想"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-5">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——习近平</span>
                                </p>
                            </div>

                            <!-- 名言6 -->
                            <div class="neumorphism-dark p-4 reveal-item hover:scale-105 transition-all duration-300" data-delay="600">
                                <p class="text-lg font-medium italic">
                                    "为中华之崛起而读书"
                                    <span class="audio-play-btn ml-2" data-audio="custom-audio-6">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                    <br><span class="text-[#ffd700] text-sm mt-2 block">——周恩来</span>
                                </p>
                            </div>
                        </div>
                    </main>
                </div>
            </div>
        </div>
    </div>

    <!-- AI智能控制栏 -->
    <div class="control-bar">
        <button class="control-btn" id="prevBtn"><i class="fas fa-chevron-left"></i></button>
        <button class="control-btn" id="playPauseBtn"><i class="fas fa-play"></i></button>
        <button class="control-btn" id="nextBtn"><i class="fas fa-chevron-right"></i></button>
        <button class="control-btn audio-btn" id="stopAudioBtn"><i class="fas fa-volume-mute"></i></button>
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        <div class="slide-info" id="slideInfo">第 1 页 / 共 4 页</div>
    </div>

    <script>
        // 全局变量
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        let currentSlide = 0;
        let currentRevealIndex = 0;
        let isPlaying = false;
        let playInterval;
        let audioPlayer = document.getElementById('audioPlayer');
        let currentAudioBtn = null;

        // DOM元素
        const slideInfo = document.getElementById('slideInfo');
        const progressBar = document.getElementById('progressBar');
        const progressContainer = document.getElementById('progressContainer');
        const playPauseBtn = document.getElementById('playPauseBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const stopAudioBtn = document.getElementById('stopAudioBtn');

        // 初始化
        function init() {
            updateSlideInfo();
            updateProgressBar();
            showSlide(0);

            // 绑定事件
            playPauseBtn.addEventListener('click', togglePlayPause);
            prevBtn.addEventListener('click', prevSlide);
            nextBtn.addEventListener('click', nextSlide);
            stopAudioBtn.addEventListener('click', stopAllAudio);
            progressContainer.addEventListener('click', handleProgressClick);

            // 键盘事件
            document.addEventListener('keydown', handleKeyPress);

            // 音频播放按钮事件
            document.querySelectorAll('.audio-play-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    playAudio(btn.dataset.audio, btn);
                });
            });
        }

        // 显示幻灯片
        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.remove('active', 'prev', 'next');
                if (i === index) {
                    slide.classList.add('active');
                } else if (i < index) {
                    slide.classList.add('prev');
                } else {
                    slide.classList.add('next');
                }
            });

            currentSlide = index;
            currentRevealIndex = 0;
            updateSlideInfo();
            updateProgressBar();

            // 重置当前幻灯片的reveal项目
            const currentSlideElement = slides[currentSlide];
            const revealItems = currentSlideElement.querySelectorAll('.reveal-item');
            revealItems.forEach(item => {
                item.classList.remove('visible');
            });

            // 开始逐步显示
            setTimeout(() => {
                revealNextItem();
            }, 500);
        }

        // 逐步显示元素
        function revealNextItem() {
            const currentSlideElement = slides[currentSlide];
            const revealItems = currentSlideElement.querySelectorAll('.reveal-item');

            if (currentRevealIndex < revealItems.length) {
                const item = revealItems[currentRevealIndex];
                const delay = parseInt(item.dataset.delay) || 0;

                setTimeout(() => {
                    item.classList.add('visible');
                    currentRevealIndex++;

                    if (isPlaying && currentRevealIndex < revealItems.length) {
                        setTimeout(() => {
                            revealNextItem();
                        }, 800);
                    }
                }, delay);
            }
        }

        // 切换播放/暂停
        function togglePlayPause() {
            if (isPlaying) {
                pausePresentation();
            } else {
                playPresentation();
            }
        }

        // 播放演示
        function playPresentation() {
            isPlaying = true;
            playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';

            // 继续显示当前幻灯片的剩余项目
            revealNextItem();

            // 设置自动播放间隔
            playInterval = setInterval(() => {
                if (currentSlide < totalSlides - 1) {
                    nextSlide();
                } else {
                    pausePresentation();
                }
            }, 8000);
        }

        // 暂停演示
        function pausePresentation() {
            isPlaying = false;
            playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            if (playInterval) {
                clearInterval(playInterval);
            }
        }

        // 上一页
        function prevSlide() {
            if (currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // 下一页
        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            }
        }

        // 更新幻灯片信息
        function updateSlideInfo() {
            slideInfo.textContent = `第 ${currentSlide + 1} 页 / 共 ${totalSlides} 页`;
        }

        // 更新进度条
        function updateProgressBar() {
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            progressBar.style.width = `${progress}%`;
        }

        // 处理进度条点击
        function handleProgressClick(e) {
            const rect = progressContainer.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const progress = clickX / rect.width;
            const targetSlide = Math.floor(progress * totalSlides);

            if (targetSlide >= 0 && targetSlide < totalSlides) {
                showSlide(targetSlide);
            }
        }

        // 键盘事件处理
        function handleKeyPress(e) {
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    prevSlide();
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    showSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    showSlide(totalSlides - 1);
                    break;
                case 'Escape':
                    e.preventDefault();
                    pausePresentation();
                    break;
            }
        }

        // 播放音频
        function playAudio(audioSrc, btn) {
            if (currentAudioBtn) {
                currentAudioBtn.classList.remove('playing');
            }

            if (audioSrc && audioSrc !== 'custom-audio-1') {
                audioPlayer.src = audioSrc;
                audioPlayer.play().then(() => {
                    btn.classList.add('playing');
                    currentAudioBtn = btn;
                }).catch(e => {
                    console.log('音频播放失败:', e);
                    // 模拟音频播放效果
                    btn.classList.add('playing');
                    currentAudioBtn = btn;
                    setTimeout(() => {
                        btn.classList.remove('playing');
                        currentAudioBtn = null;
                    }, 3000);
                });
            } else {
                // 模拟音频播放效果
                btn.classList.add('playing');
                currentAudioBtn = btn;
                setTimeout(() => {
                    btn.classList.remove('playing');
                    currentAudioBtn = null;
                }, 3000);
            }
        }

        // 停止所有音频
        function stopAllAudio() {
            audioPlayer.pause();
            audioPlayer.currentTime = 0;
            if (currentAudioBtn) {
                currentAudioBtn.classList.remove('playing');
                currentAudioBtn = null;
            }
        }

        // 音频结束事件
        audioPlayer.addEventListener('ended', () => {
            if (currentAudioBtn) {
                currentAudioBtn.classList.remove('playing');
                currentAudioBtn = null;
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
